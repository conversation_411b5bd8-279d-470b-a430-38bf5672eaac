#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动批量调试脚本功能测试脚本
测试新的自动检测运行中设备的批量操作功能
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:5000"
TEST_DEVICE_IDS = [1, 2, 3]  # 替换为实际的设备ID

def test_get_running_devices():
    """测试获取运行中调试脚本设备列表API"""
    print("=" * 50)
    print("测试获取运行中调试脚本设备列表API")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/debug_script/api/running_devices")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"请求成功: {data.get('success')}")
            
            if data.get('success'):
                running_devices = data.get('running_devices', [])
                total_count = data.get('total_count', 0)
                
                print(f"运行中设备数量: {total_count}")
                print("\n运行中设备列表:")
                
                if running_devices:
                    for device in running_devices:
                        print(f"  设备ID: {device.get('device_id')}")
                        print(f"  设备备注: {device.get('device_remark', '无')}")
                        print(f"  执行频率: {device.get('frequency')}秒")
                        print(f"  最后执行状态: {device.get('last_execution_status')}")
                        print(f"  总执行次数: {device.get('total_executions')}")
                        print(f"  成功执行次数: {device.get('successful_executions')}")
                        print("  " + "-" * 30)
                else:
                    print("  当前没有正在运行调试脚本的设备")
            else:
                print(f"请求失败: {data.get('error')}")
        else:
            print(f"HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"异常: {e}")

def start_test_debug_scripts():
    """启动一些测试调试脚本"""
    print("\n" + "=" * 50)
    print("启动测试调试脚本")
    print("=" * 50)
    
    started_devices = []
    
    for device_id in TEST_DEVICE_IDS:
        try:
            print(f"启动设备 {device_id} 的调试脚本...")
            
            response = requests.post(
                f"{BASE_URL}/debug_script/start/{device_id}",
                headers={'Content-Type': 'application/json'},
                data=json.dumps({'frequency': 60})
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"  ✓ 设备 {device_id} 调试脚本启动成功")
                    started_devices.append(device_id)
                else:
                    print(f"  ✗ 设备 {device_id} 调试脚本启动失败: {data.get('message')}")
            else:
                print(f"  ✗ 设备 {device_id} HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ✗ 设备 {device_id} 异常: {e}")
    
    print(f"\n成功启动 {len(started_devices)} 个设备的调试脚本")
    return started_devices

def stop_test_debug_scripts(device_ids):
    """停止测试调试脚本"""
    print("\n" + "=" * 50)
    print("停止测试调试脚本")
    print("=" * 50)
    
    stopped_devices = []
    
    for device_id in device_ids:
        try:
            print(f"停止设备 {device_id} 的调试脚本...")
            
            response = requests.post(f"{BASE_URL}/debug_script/stop/{device_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"  ✓ 设备 {device_id} 调试脚本停止成功")
                    stopped_devices.append(device_id)
                else:
                    print(f"  ✗ 设备 {device_id} 调试脚本停止失败: {data.get('message')}")
            else:
                print(f"  ✗ 设备 {device_id} HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"  ✗ 设备 {device_id} 异常: {e}")
    
    print(f"\n成功停止 {len(stopped_devices)} 个设备的调试脚本")
    return stopped_devices

def test_auto_batch_workflow():
    """测试自动批量操作工作流程"""
    print("\n" + "=" * 50)
    print("测试自动批量操作工作流程")
    print("=" * 50)
    
    # 1. 检查初始状态
    print("1. 检查初始运行状态...")
    test_get_running_devices()
    
    # 2. 启动一些调试脚本
    print("\n2. 启动测试调试脚本...")
    started_devices = start_test_debug_scripts()
    
    if not started_devices:
        print("没有成功启动任何调试脚本，跳过后续测试")
        return
    
    # 等待脚本启动
    print("\n等待3秒让调试脚本完全启动...")
    time.sleep(3)
    
    # 3. 再次检查运行状态
    print("\n3. 检查启动后的运行状态...")
    test_get_running_devices()
    
    # 4. 模拟前端自动检测和批量操作
    print("\n4. 模拟前端自动检测流程...")
    
    try:
        # 获取运行中设备列表
        response = requests.get(f"{BASE_URL}/debug_script/api/running_devices")
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                running_devices = data.get('running_devices', [])
                
                if running_devices:
                    print(f"检测到 {len(running_devices)} 个运行中的设备")
                    
                    # 模拟用户确认操作
                    print("模拟用户确认批量停止操作...")
                    
                    # 执行批量停止
                    device_ids = [device['id'] for device in running_devices]
                    stop_test_debug_scripts(device_ids)
                    
                else:
                    print("没有检测到运行中的设备")
            else:
                print(f"获取运行中设备失败: {data.get('error')}")
        else:
            print(f"API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"模拟前端流程异常: {e}")
    
    # 5. 最终状态检查
    print("\n5. 检查最终运行状态...")
    time.sleep(2)  # 等待停止操作完成
    test_get_running_devices()

def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 50)
    print("测试边界情况")
    print("=" * 50)
    
    # 测试1: 没有运行中设备时的API响应
    print("1. 测试没有运行中设备时的API响应...")
    
    # 确保没有运行中的调试脚本
    for device_id in TEST_DEVICE_IDS:
        try:
            requests.post(f"{BASE_URL}/debug_script/stop/{device_id}")
        except:
            pass
    
    time.sleep(2)
    test_get_running_devices()
    
    # 测试2: API错误处理
    print("\n2. 测试API错误处理...")
    
    try:
        # 测试无效的API端点
        response = requests.get(f"{BASE_URL}/debug_script/api/invalid_endpoint")
        print(f"无效端点响应状态码: {response.status_code}")
        
    except Exception as e:
        print(f"无效端点测试异常: {e}")

def main():
    """主测试函数"""
    print(f"自动批量调试脚本功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试服务器: {BASE_URL}")
    print(f"测试设备: {TEST_DEVICE_IDS}")
    
    # 执行测试
    test_auto_batch_workflow()
    test_edge_cases()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    print("\n前端功能测试步骤:")
    print("1. 访问设备管理页面")
    print("2. 点击'重启运行中的调试脚本'按钮")
    print("3. 查看是否显示正确的设备列表和确认对话框")
    print("4. 确认操作后查看结果模态框")
    print("5. 点击'停止运行中的调试脚本'按钮")
    print("6. 验证操作结果")
    
    print("\n预期行为:")
    print("- 如果没有运行中的设备，显示提示信息")
    print("- 如果有运行中的设备，显示设备列表确认对话框")
    print("- 操作结果模态框显示详细的执行结果")
    print("- 按钮文本清楚表明是自动检测功能")
    
    print("\n新功能特点:")
    print("✓ 自动检测运行中的调试脚本设备")
    print("✓ 无需用户手动选择设备")
    print("✓ 清晰的状态提示和确认对话框")
    print("✓ 详细的操作结果反馈")

if __name__ == "__main__":
    main()
