#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时序数据库初始化脚本
创建时序数据表和相关索引
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models.database import db
from models.time_series_data import TimeSeriesData, TimeSeriesDataBatch
from config import Config
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 初始化数据库
    db.init_app(app)
    
    return app


def init_time_series_tables():
    """初始化时序数据表"""
    try:
        app = create_app()
        
        with app.app_context():
            logger.info("开始初始化时序数据表...")
            
            # 创建表
            db.create_all()
            
            # 检查表是否创建成功
            inspector = db.inspect(db.engine)
            
            # 获取当前环境的表名
            dev_table = 'dev_time_series_data'
            prod_table = 'prod_time_series_data'
            dev_batch_table = 'dev_time_series_batch'
            prod_batch_table = 'prod_time_series_batch'
            
            existing_tables = inspector.get_table_names()
            
            # 检查开发环境表
            if dev_table in existing_tables:
                logger.info(f"开发环境时序数据表 {dev_table} 创建成功")
            else:
                logger.warning(f"开发环境时序数据表 {dev_table} 未找到")
            
            # 检查生产环境表
            if prod_table in existing_tables:
                logger.info(f"生产环境时序数据表 {prod_table} 创建成功")
            else:
                logger.warning(f"生产环境时序数据表 {prod_table} 未找到")
            
            # 检查批次表
            if dev_batch_table in existing_tables:
                logger.info(f"开发环境批次表 {dev_batch_table} 创建成功")
            if prod_batch_table in existing_tables:
                logger.info(f"生产环境批次表 {prod_batch_table} 创建成功")
            
            # 创建额外的索引（如果需要）
            create_additional_indexes()
            
            logger.info("时序数据表初始化完成")
            return True
            
    except Exception as e:
        logger.error(f"初始化时序数据表失败: {e}")
        return False


def create_additional_indexes():
    """创建额外的索引以优化查询性能"""
    try:
        # 获取当前环境
        env = os.environ.get('FLASK_ENV', 'production')
        table_prefix = 'dev_' if env == 'development' else 'prod_'
        
        # 创建分区索引（按日期分区）
        partition_indexes = [
            f"""
            CREATE INDEX IF NOT EXISTS idx_{table_prefix}ts_data_date_partition 
            ON {table_prefix}time_series_data (DATE(timestamp), device_id, data_type);
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_{table_prefix}ts_data_device_date 
            ON {table_prefix}time_series_data (device_id, DATE(timestamp));
            """,
            f"""
            CREATE INDEX IF NOT EXISTS idx_{table_prefix}ts_data_numeric_range 
            ON {table_prefix}time_series_data (data_type, numeric_value) 
            WHERE numeric_value IS NOT NULL;
            """
        ]
        
        for index_sql in partition_indexes:
            try:
                db.session.execute(index_sql)
                logger.info(f"创建索引成功: {index_sql.strip()[:50]}...")
            except Exception as e:
                logger.warning(f"创建索引失败: {e}")
        
        db.session.commit()
        
    except Exception as e:
        logger.error(f"创建额外索引失败: {e}")
        db.session.rollback()


def migrate_existing_data():
    """迁移现有的Excel数据到新的时序数据库（可选）"""
    try:
        logger.info("开始迁移现有数据...")
        
        # 这里可以添加从Excel文件迁移数据的逻辑
        # 由于要求不迁移旧数据，这里只是预留接口
        
        logger.info("数据迁移完成（跳过旧数据）")
        return True
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        return False


def verify_installation():
    """验证安装是否成功"""
    try:
        app = create_app()
        
        with app.app_context():
            # 测试插入一条数据
            test_data = TimeSeriesData(
                device_id='test_device',
                timestamp=datetime.now(),
                data_type='test',
                data_value={'test': True},
                numeric_value=1.0
            )
            
            db.session.add(test_data)
            db.session.commit()
            
            # 测试查询
            result = db.session.query(TimeSeriesData).filter(
                TimeSeriesData.device_id == 'test_device'
            ).first()
            
            if result:
                logger.info("时序数据库验证成功")
                
                # 清理测试数据
                db.session.delete(result)
                db.session.commit()
                
                return True
            else:
                logger.error("时序数据库验证失败：无法查询到测试数据")
                return False
                
    except Exception as e:
        logger.error(f"时序数据库验证失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("时序数据库初始化脚本")
    print("=" * 60)
    
    # 设置环境变量
    env = input("请选择环境 (development/production) [development]: ").strip()
    if not env:
        env = 'development'
    
    os.environ['FLASK_ENV'] = env
    print(f"当前环境: {env}")
    
    # 初始化表
    print("\n1. 初始化时序数据表...")
    if init_time_series_tables():
        print("✓ 时序数据表初始化成功")
    else:
        print("✗ 时序数据表初始化失败")
        return False
    
    # 验证安装
    print("\n2. 验证安装...")
    if verify_installation():
        print("✓ 安装验证成功")
    else:
        print("✗ 安装验证失败")
        return False
    
    print("\n" + "=" * 60)
    print("时序数据库初始化完成！")
    print("=" * 60)
    
    print("\n下一步操作：")
    print("1. 更新调试脚本管理器以使用新的时序数据服务")
    print("2. 更新路由以使用新的查询接口")
    print("3. 测试数据写入和查询功能")
    
    return True


if __name__ == '__main__':
    main()
