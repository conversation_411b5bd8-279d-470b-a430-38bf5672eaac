{% extends "base.html" %}

{% block title %}固件管理{% endblock %}

{% block styles %}
<style>
    /* 固件列表页面优化样式 */
    .firmware-table-container {
        min-width: 1000px; /* 确保表格有足够的宽度 */
    }

    .firmware-table th,
    .firmware-table td {
        white-space: nowrap; /* 防止内容换行 */
        vertical-align: middle;
        padding: 0.75rem 0.5rem;
    }

    /* 列宽优化 */
    .firmware-table th:nth-child(1), .firmware-table td:nth-child(1) { /* 名称 */
        width: 200px;
        min-width: 200px;
        max-width: 250px;
        white-space: normal; /* 名称可以换行 */
        word-break: break-word;
    }

    .firmware-table th:nth-child(2), .firmware-table td:nth-child(2) { /* 版本 */
        width: 100px;
        min-width: 100px;
        text-align: center;
    }

    .firmware-table th:nth-child(3), .firmware-table td:nth-child(3) { /* 大小 */
        width: 100px;
        min-width: 100px;
        text-align: right;
    }

    .firmware-table th:nth-child(4), .firmware-table td:nth-child(4) { /* CRC32 */
        width: 120px;
        min-width: 120px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
    }

    .firmware-table th:nth-child(5), .firmware-table td:nth-child(5) { /* 上传时间 */
        width: 150px;
        min-width: 150px;
        text-align: center;
    }

    .firmware-table th:nth-child(6), .firmware-table td:nth-child(6) { /* 下载次数 */
        width: 100px;
        min-width: 100px;
        text-align: center;
        font-weight: 600;
    }

    .firmware-table th:nth-child(7), .firmware-table td:nth-child(7) { /* 操作 */
        width: 150px;
        min-width: 150px;
        text-align: center;
    }

    /* 下载次数样式优化 */
    .download-count {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        color: #1976d2;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.85rem;
        font-weight: 600;
        min-width: 40px;
    }

    /* 操作按钮组优化 */
    .firmware-actions {
        display: flex;
        gap: 4px;
        justify-content: center;
        flex-wrap: nowrap;
    }

    .firmware-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 4px;
        min-width: 60px;
    }

    /* 响应式优化 */
    @media (max-width: 1200px) {
        .firmware-table th:nth-child(4), .firmware-table td:nth-child(4) { /* 隐藏CRC32列 */
            display: none;
        }
    }

    @media (max-width: 992px) {
        .firmware-table th:nth-child(5), .firmware-table td:nth-child(5) { /* 隐藏上传时间列 */
            display: none;
        }
    }

    @media (max-width: 768px) {
        .firmware-actions {
            flex-direction: column;
            gap: 2px;
        }

        .firmware-actions .btn {
            min-width: auto;
            padding: 0.2rem 0.4rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">固件管理</h2>
    
    <!-- 上传固件表单 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">上传新固件</h5>
        </div>
        <div class="card-body">
            <form action="{{ url_for('firmware.upload_firmware') }}" method="post" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="firmware_name" class="form-label">固件名称</label>
                            <input type="text" class="form-control" id="firmware_name" name="firmware_name" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="firmware_file" class="form-label">固件文件</label>
                            <input type="file" class="form-control" id="firmware_file" name="firmware_file" required accept=".bin">
                            <div class="form-text">支持.bin格式的固件文件，系统将自动从固件中读取版本信息</div>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="firmware_description" class="form-label">描述</label>
                    <textarea class="form-control" id="firmware_description" name="firmware_description" rows="2"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">上传固件</button>
            </form>
        </div>
    </div>

    <!-- 固件列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">固件列表</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive firmware-table-container">
                <table class="table table-striped table-hover firmware-table">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-file-code me-1"></i>名称</th>
                            <th><i class="fas fa-tag me-1"></i>版本</th>
                            <th><i class="fas fa-weight me-1"></i>大小</th>
                            <th><i class="fas fa-fingerprint me-1"></i>CRC32</th>
                            <th><i class="fas fa-clock me-1"></i>上传时间</th>
                            <th><i class="fas fa-download me-1"></i>下载次数</th>
                            <th><i class="fas fa-cogs me-1"></i>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for firmware in firmwares %}
                        <tr>
                            <td class="align-middle">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-microchip text-primary me-2"></i>
                                    <span title="{{ firmware.name }}">{{ firmware.name }}</span>
                                </div>
                            </td>
                            <td class="align-middle">
                                <span class="badge bg-primary">v{{ firmware.version }}</span>
                            </td>
                            <td class="align-middle">
                                <span class="text-muted">{{ (firmware.size / 1024)|round(2) }} KB</span>
                            </td>
                            <td class="align-middle">
                                <code class="text-muted">{{ firmware.crc32 }}</code>
                            </td>
                            <td class="align-middle">
                                <small class="text-muted">{{ firmware.upload_time.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                            </td>
                            <td class="align-middle">
                                <span class="download-count">
                                    <i class="fas fa-download me-1"></i>{{ firmware.download_count }}
                                </span>
                            </td>
                            <td class="align-middle">
                                <div class="firmware-actions">
                                    <a href="{{ url_for('firmware.download_firmware', id=firmware.id) }}"
                                       class="btn btn-sm btn-primary" title="下载固件">
                                        <i class="fas fa-download"></i> 下载
                                    </a>
                                    <a href="{{ url_for('firmware.delete_firmware', id=firmware.id) }}"
                                       class="btn btn-sm btn-danger" title="删除固件"
                                       onclick="return confirm('确定要删除这个固件吗？')">
                                        <i class="fas fa-trash"></i> 删除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 