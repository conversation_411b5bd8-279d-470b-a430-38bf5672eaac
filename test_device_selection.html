<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备选择功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>设备选择功能测试</h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4>测试DeviceSelectionManager</h4>
                <div class="mb-3">
                    <button id="testAddDevice" class="btn btn-primary">添加设备 (ID: 123)</button>
                    <button id="testRemoveDevice" class="btn btn-danger">移除设备 (ID: 123)</button>
                    <button id="testClearAll" class="btn btn-warning">清空所有</button>
                </div>
                
                <div class="mb-3">
                    <button id="testGetInfo" class="btn btn-info">获取设备信息</button>
                </div>
                
                <div class="mb-3">
                    <strong>选中设备数量: </strong><span id="selectedCount">0</span>
                </div>
                
                <div id="deviceInfo" class="mt-3"></div>
            </div>
            
            <div class="col-md-6">
                <h4>测试结果</h4>
                <div id="testResults" class="border p-3" style="height: 400px; overflow-y: auto;">
                    <!-- 测试结果将显示在这里 -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟全局变量
        let selectedDevices = new Set();

        // 设备选择状态管理器
        const DeviceSelectionManager = {
            STORAGE_KEY: 'device_selection_state',
            
            // 从sessionStorage加载选择状态
            loadSelection: function() {
                try {
                    const stored = sessionStorage.getItem(this.STORAGE_KEY);
                    if (stored) {
                        const data = JSON.parse(stored);
                        selectedDevices = new Set(data.selectedDevices || []);
                        this.updateSelectionDisplay();
                        this.log(`已加载 ${selectedDevices.size} 个选中设备`);
                    }
                } catch (error) {
                    this.log('加载设备选择状态失败: ' + error.message);
                    selectedDevices = new Set();
                }
            },
            
            // 保存选择状态到sessionStorage
            saveSelection: function() {
                try {
                    const data = {
                        selectedDevices: Array.from(selectedDevices),
                        timestamp: Date.now()
                    };
                    sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
                } catch (error) {
                    this.log('保存设备选择状态失败: ' + error.message);
                }
            },
            
            // 添加设备到选择列表
            addDevice: function(deviceId) {
                selectedDevices.add(deviceId);
                this.saveSelection();
                this.updateSelectionDisplay();
                this.log(`添加设备: ${deviceId}`);
            },
            
            // 从选择列表移除设备
            removeDevice: function(deviceId) {
                selectedDevices.delete(deviceId);
                this.saveSelection();
                this.updateSelectionDisplay();
                this.log(`移除设备: ${deviceId}`);
            },
            
            // 清空选择
            clearSelection: function() {
                selectedDevices.clear();
                this.saveSelection();
                this.updateSelectionDisplay();
                this.log('清空所有选择');
            },
            
            // 更新选择显示
            updateSelectionDisplay: function() {
                const countElement = document.getElementById('selectedCount');
                if (countElement) {
                    countElement.textContent = selectedDevices.size;
                }
            },
            
            // 获取选中设备的详细信息（模拟）
            getSelectedDevicesInfo: async function() {
                if (selectedDevices.size === 0) {
                    return [];
                }
                
                // 模拟API调用
                this.log('模拟获取设备信息...');
                
                return Array.from(selectedDevices).map(id => ({
                    id: id,
                    device_id: `设备${id}`,
                    device_remark: `设备${id}的备注`,
                    product_key: 'test_product_key'
                }));
            },
            
            // 日志函数
            log: function(message) {
                const resultsDiv = document.getElementById('testResults');
                const time = new Date().toLocaleTimeString();
                resultsDiv.innerHTML += `<div>[${time}] ${message}</div>`;
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            DeviceSelectionManager.loadSelection();
            
            // 绑定测试按钮事件
            document.getElementById('testAddDevice').addEventListener('click', function() {
                DeviceSelectionManager.addDevice('123');
            });
            
            document.getElementById('testRemoveDevice').addEventListener('click', function() {
                DeviceSelectionManager.removeDevice('123');
            });
            
            document.getElementById('testClearAll').addEventListener('click', function() {
                DeviceSelectionManager.clearSelection();
            });
            
            document.getElementById('testGetInfo').addEventListener('click', async function() {
                try {
                    const devicesInfo = await DeviceSelectionManager.getSelectedDevicesInfo();
                    const infoDiv = document.getElementById('deviceInfo');
                    
                    if (devicesInfo.length === 0) {
                        infoDiv.innerHTML = '<div class="alert alert-info">没有选中的设备</div>';
                    } else {
                        let html = '<div class="alert alert-success">设备信息:</div>';
                        devicesInfo.forEach(device => {
                            html += `
                                <div class="card mb-2">
                                    <div class="card-body">
                                        <h6 class="card-title">${device.device_id}</h6>
                                        <p class="card-text">${device.device_remark}</p>
                                        <small class="text-muted">产品密钥: ${device.product_key}</small>
                                    </div>
                                </div>
                            `;
                        });
                        infoDiv.innerHTML = html;
                    }
                    
                    DeviceSelectionManager.log(`获取到 ${devicesInfo.length} 个设备的信息`);
                } catch (error) {
                    DeviceSelectionManager.log('获取设备信息失败: ' + error.message);
                }
            });
            
            DeviceSelectionManager.log('测试页面初始化完成');
        });
    </script>
</body>
</html>
