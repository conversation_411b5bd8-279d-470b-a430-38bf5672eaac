{% extends "base.html" %}
<!-- stylelint-disable -->

{% block title %}OTA任务 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2"><i class="fas fa-sync-alt"></i> OTA任务</h2>
    </div>
</div>

<div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0"><i class="fas fa-list"></i> 任务列表</h5>
                        </div>
                        <div class="col-auto">
                            <div class="row g-3">
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="date" class="form-control" id="dateFilter">
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-filter"></i></span>
                                        <select class="form-select" id="statusFilter">
                                            <option value="">全部状态</option>
                                            <option value="成功">成功</option>
                                            <option value="失败">失败</option>
                                            <option value="等待中">等待中</option>
                                            <option value="进行中">进行中</option>
                                            <option value="已暂停">已暂停</option>
                                            <option value="已取消">已取消</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="deviceIdFilter" placeholder="设备ID">
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <button type="button" class="btn btn-outline-info" onclick="testWebSocket()" title="测试WebSocket连接">
                                        <i class="fas fa-wifi me-1"></i>测试连接
                                    </button>
                                </div>
                                <div class="col-auto">
                                    <span id="websocketStatus" class="badge bg-secondary" title="WebSocket连接状态">
                                        <i class="fas fa-circle me-1"></i>连接中...
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 加载指示器 -->
                    <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">正在加载任务列表...</div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>任务ID</th>
                                    <th>设备ID</th>
                                    <th>固件版本</th>
                                    <th>状态</th>
                                    <th>进度</th>
                                    <th>创建时间</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="taskTableBody">
                                <!-- 任务列表将通过Ajax动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控件 -->
                    <div class="d-flex justify-content-between align-items-center p-3 border-top flex-wrap gap-3">
                        <div class="text-muted">
                            <span id="paginationInfo">显示第 1-20 条，共 0 条记录</span>
                        </div>

                        <!-- 页码跳转输入框 -->
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-muted small">跳转到</span>
                            <div class="input-group input-group-sm" style="width: 120px;">
                                <input type="number" class="form-control" id="pageJumpInput" min="1" placeholder="1-1"
                                       onkeypress="if(event.key==='Enter') jumpToPage()">
                                <button class="btn btn-outline-secondary" type="button" onclick="jumpToPage()">
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>
                            <span class="text-muted small">共 <span id="totalPagesSpan">0</span> 页</span>
                        </div>

                        <nav aria-label="任务列表分页">
                            <ul class="pagination pagination-lg mb-0" id="paginationControls">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* 分页按钮优化样式 */
    .pagination-lg .page-link {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        min-width: 48px;
        min-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem;
        margin: 0 2px;
        transition: all 0.2s ease-in-out;
    }

    .pagination-lg .page-link:hover {
        background-color: #e9ecef;
        border-color: #dee2e6;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .pagination-lg .page-item.active .page-link {
        background-color: #0d6efd;
        border-color: #0d6efd;
        box-shadow: 0 2px 4px rgba(13,110,253,0.25);
    }

    .pagination-lg .page-item.disabled .page-link {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 分页按钮图标样式 */
    .pagination-lg .page-link i {
        font-size: 1.1rem;
    }

    /* 响应式分页 */
    @media (max-width: 768px) {
        .pagination-lg .page-link {
            padding: 0.5rem 0.75rem;
            min-width: 40px;
            min-height: 40px;
            font-size: 0.9rem;
        }
    }

    /* 深色模式下的分页样式 */
    body.dark-mode .pagination-lg .page-link {
        background-color: #495057;
        border-color: #6c757d;
        color: #fff;
    }

    body.dark-mode .pagination-lg .page-link:hover {
        background-color: #6c757d;
        border-color: #adb5bd;
    }

    body.dark-mode .pagination-lg .page-item.active .page-link {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    /* 固件CRC信息样式 */
    .firmware-info {
        min-width: 120px;
    }

    .firmware-crc {
        font-family: 'Courier New', monospace;
        font-size: 0.75rem;
        color: #6c757d;
        background-color: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
        border: 1px solid #dee2e6;
        display: inline-block;
        margin-top: 2px;
    }

    body.dark-mode .firmware-crc {
        color: #adb5bd;
        background-color: #343a40;
        border-color: #495057;
    }

    .firmware-crc.unknown {
        color: #dc3545;
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }

    body.dark-mode .firmware-crc.unknown {
        color: #f5c2c7;
        background-color: #2c0b0e;
        border-color: #842029;
    }
</style>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskDetailsModalLabel">
                    <i class="fas fa-info-circle"></i> 任务详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>任务ID:</strong> <span id="detailTaskId"></span></p>
                        <p><strong>设备ID:</strong> <span id="detailDeviceId"></span></p>
                        <p><strong>固件版本:</strong> <span id="detailFirmwareVersion"></span></p>
                        <p><strong>固件CRC:</strong> <span id="detailFirmwareCrc" class="firmware-crc"></span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>状态:</strong> <span id="detailStatus"></span></p>
                        <p><strong>进度:</strong> <span id="detailProgress"></span>%</p>
                        <p><strong>创建时间:</strong> <span id="detailCreatedAt"></span></p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <p><strong>错误信息:</strong></p>
                        <pre id="detailError" class="bg-light p-2" style="max-height: 200px; overflow-y: auto;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 通知组件 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="notificationIcon" class="fas fa-info-circle me-2"></i>
            <strong id="notificationTitle" class="me-auto">通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="notificationMessage">
            <!-- 通知内容 -->
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
<script>
    // 打印调试日志到控制台
    console.log("OTA任务脚本加载中...");

    // 全局变量定义
    let taskDetailsModal = null;
    let socket = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    let currentPage = 1;
    let currentFilters = {};
    let notificationToast = null;
    
    // 工具函数
    const Utils = {
        // 防抖函数
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 通用的API请求函数
        apiRequest: async function(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            };

            const finalOptions = { ...defaultOptions, ...options };

            try {
                const response = await fetch(url, finalOptions);

                if (checkLoginRedirect(response)) {
                    throw new Error('需要登录');
                }

                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error(`API请求失败 ${url}:`, error);
                throw error;
            }
        },

        // 格式化时间戳
        formatTimestamp: function(timestamp) {
            if (!timestamp) return '--';
            return new Date(timestamp).toLocaleString();
        }
    };

    // 处理筛选条件变化
    function handleFilterChange() {
        currentFilters = {
            search: '', // OTA任务页面暂时不需要通用搜索
            status: document.getElementById('statusFilter').value,
            device_id: document.getElementById('deviceIdFilter').value.trim(),
            firmware: '', // 暂时不需要固件版本筛选
            date: document.getElementById('dateFilter').value
        };

        currentPage = 1; // 重置到第一页
        loadTaskList(currentPage);
    }

    // 添加筛选事件监听器
    function setupEventListeners() {
        document.getElementById('dateFilter').addEventListener('change', handleFilterChange);
        document.getElementById('statusFilter').addEventListener('change', handleFilterChange);
        document.getElementById('deviceIdFilter').addEventListener('keyup', Utils.debounce(handleFilterChange, 500));
    }

    // 显示通知
    function showNotification(message, type = 'info', title = '通知') {
        const toast = document.getElementById('notificationToast');
        const icon = document.getElementById('notificationIcon');
        const titleEl = document.getElementById('notificationTitle');
        const messageEl = document.getElementById('notificationMessage');

        // 设置图标和样式
        toast.className = 'toast';
        if (type === 'success') {
            toast.classList.add('bg-success', 'text-white');
            icon.className = 'fas fa-check-circle me-2';
        } else if (type === 'error') {
            toast.classList.add('bg-danger', 'text-white');
            icon.className = 'fas fa-exclamation-circle me-2';
        } else if (type === 'warning') {
            toast.classList.add('bg-warning', 'text-dark');
            icon.className = 'fas fa-exclamation-triangle me-2';
        } else {
            toast.classList.add('bg-info', 'text-white');
            icon.className = 'fas fa-info-circle me-2';
        }

        titleEl.textContent = title;
        messageEl.textContent = message;

        if (!notificationToast) {
            notificationToast = new bootstrap.Toast(toast);
        }
        notificationToast.show();
    }

    // 显示/隐藏加载状态
    function showLoading(show) {
        const loadingIndicator = document.getElementById('loadingIndicator');
        const tableBody = document.getElementById('taskTableBody');

        if (show) {
            loadingIndicator.style.display = 'block';
            tableBody.style.opacity = '0.5';
        } else {
            loadingIndicator.style.display = 'none';
            tableBody.style.opacity = '1';
        }
    }

    // 加载任务列表
    function loadTaskList(page = 1) {
        showLoading(true);

        // 构建查询参数
        const params = new URLSearchParams({
            page: page,
            per_page: 20,
            ...currentFilters
        });

        fetch(`/api/ota/tasks?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderTaskList(data.tasks);
                    renderPagination(data.pagination);
                    currentPage = page;
                } else {
                    showNotification('加载任务列表失败: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('加载任务列表失败:', error);
                showNotification('加载任务列表失败，请重试', 'error');
            })
            .finally(() => {
                showLoading(false);
            });
    }

    // 渲染任务列表 - 优化版本
    function renderTaskList(tasks) {
        const tbody = document.getElementById('taskTableBody');

        // 使用DocumentFragment提高性能
        const fragment = document.createDocumentFragment();

        // 批量创建行
        tasks.forEach(task => {
            const row = createTaskRowOptimized(task);
            fragment.appendChild(row);
        });

        // 一次性替换所有内容
        tbody.innerHTML = '';
        tbody.appendChild(fragment);
    }

    // 创建任务行 - 优化版本
    function createTaskRowOptimized(task) {
        const row = document.createElement('tr');
        row.setAttribute('data-task-id', task.id);

        // 使用对象映射提高状态标签性能
        const statusHtmlMap = {
            '成功': '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>成功</span>',
            '失败': '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>失败</span>',
            '等待中': '<span class="badge bg-secondary"><i class="fas fa-hourglass-half me-1"></i>等待中</span>',
            '已暂停': '<span class="badge bg-warning"><i class="fas fa-pause me-1"></i>已暂停</span>',
            '已取消': '<span class="badge bg-dark"><i class="fas fa-ban me-1"></i>已取消</span>',
            'default': '<span class="badge bg-info"><i class="fas fa-sync fa-spin me-1"></i>进行中</span>'
        };

        const statusHtml = statusHtmlMap[task.status] || statusHtmlMap.default;

        // 预计算进度条样式
        const progressBarClass = task.status === '进行中' ? 'progress-bar-striped progress-bar-animated' : 'progress-bar-striped';

        // 预计算操作按钮
        const retryButton = task.status === '失败'
            ? `<button type="button" class="btn btn-sm btn-warning" onclick="retryTask('${task.id}')" title="重试">
                    <i class="fas fa-redo"></i>
               </button>`
            : '';

        // 暂停按钮 - 只在进行中状态显示
        const pauseButton = task.status === '进行中'
            ? `<button type="button" class="btn btn-sm btn-secondary" onclick="pauseTask('${task.id}')" title="暂停">
                    <i class="fas fa-pause"></i>
               </button>`
            : '';

        // 恢复按钮 - 只在已暂停状态显示
        const resumeButton = task.status === '已暂停'
            ? `<button type="button" class="btn btn-sm btn-success" onclick="resumeTask('${task.id}')" title="恢复">
                    <i class="fas fa-play"></i>
               </button>`
            : '';

        // 取消按钮 - 在等待中、进行中、已暂停状态显示
        const cancelButton = ['等待中', '进行中', '已暂停'].includes(task.status)
            ? `<button type="button" class="btn btn-sm btn-outline-danger" onclick="cancelTask('${task.id}')" title="取消">
                    <i class="fas fa-times"></i>
               </button>`
            : '';

        // 使用模板字符串一次性构建HTML
        row.innerHTML = `
            <td>${task.id}</td>
            <td>${task.device_name}</td>
            <td class="firmware-info">
                <div class="d-flex flex-column align-items-start">
                    <span class="badge bg-primary mb-1">v${task.firmware_version}</span>
                    ${task.firmware_crc
                        ? `<span class="firmware-crc">${task.firmware_crc}</span>`
                        : '<span class="firmware-crc unknown">CRC未知</span>'
                    }
                </div>
            </td>
            <td>${statusHtml}</td>
            <td style="width: 200px;">
                <div class="progress">
                    <div class="progress-bar ${progressBarClass}"
                         role="progressbar"
                         data-progress="${task.progress}"
                         aria-valuenow="${task.progress}"
                         aria-valuemin="0"
                         aria-valuemax="100"
                         style="width: ${task.progress}%">
                        ${task.progress}%
                    </div>
                </div>
            </td>
            <td>${task.created_at}</td>
            <td>${task.updated_at}</td>
            <td>
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-info" onclick="viewTaskDetails('${task.id}')" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${retryButton}
                    ${pauseButton}
                    ${resumeButton}
                    ${cancelButton}
                    <button type="button" class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        return row;
    }



    // 渲染分页控件
    function renderPagination(pagination) {
        const paginationInfo = document.getElementById('paginationInfo');
        const paginationControls = document.getElementById('paginationControls');
        const totalPagesSpan = document.getElementById('totalPagesSpan');
        const pageJumpInput = document.getElementById('pageJumpInput');

        // 更新分页信息
        const start = (pagination.page - 1) * pagination.per_page + 1;
        const end = Math.min(pagination.page * pagination.per_page, pagination.total);
        paginationInfo.textContent = `显示第 ${start}-${end} 条，共 ${pagination.total} 条记录`;

        // 更新总页数显示
        if (totalPagesSpan) {
            totalPagesSpan.textContent = pagination.pages;
        }

        // 设置页码输入框的最大值
        if (pageJumpInput) {
            pageJumpInput.max = pagination.pages;
            pageJumpInput.placeholder = `1-${pagination.pages}`;
        }

        // 生成分页按钮
        paginationControls.innerHTML = '';

        // 首页按钮
        const firstLi = document.createElement('li');
        firstLi.className = `page-item ${pagination.page === 1 ? 'disabled' : ''}`;
        firstLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(1)" title="首页">
            <i class="fas fa-angle-double-left"></i>
        </a>`;
        paginationControls.appendChild(firstLi);

        // 上一页按钮
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${!pagination.has_prev ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${pagination.prev_num || 1})" title="上一页">
            <i class="fas fa-angle-left"></i>
        </a>`;
        paginationControls.appendChild(prevLi);

        // 页码按钮
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        // 如果起始页不是1，显示省略号
        if (startPage > 1) {
            const li = document.createElement('li');
            li.className = 'page-item';
            li.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(1)">1</a>`;
            paginationControls.appendChild(li);

            if (startPage > 2) {
                const ellipsisLi = document.createElement('li');
                ellipsisLi.className = 'page-item disabled';
                ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                paginationControls.appendChild(ellipsisLi);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === pagination.page ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${i})">${i}</a>`;
            paginationControls.appendChild(li);
        }

        // 如果结束页不是最后一页，显示省略号
        if (endPage < pagination.pages) {
            if (endPage < pagination.pages - 1) {
                const ellipsisLi = document.createElement('li');
                ellipsisLi.className = 'page-item disabled';
                ellipsisLi.innerHTML = `<span class="page-link">...</span>`;
                paginationControls.appendChild(ellipsisLi);
            }

            const li = document.createElement('li');
            li.className = 'page-item';
            li.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${pagination.pages})">${pagination.pages}</a>`;
            paginationControls.appendChild(li);
        }

        // 下一页按钮
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${!pagination.has_next ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${pagination.next_num || pagination.pages})" title="下一页">
            <i class="fas fa-angle-right"></i>
        </a>`;
        paginationControls.appendChild(nextLi);

        // 末页按钮
        const lastLi = document.createElement('li');
        lastLi.className = `page-item ${pagination.page === pagination.pages ? 'disabled' : ''}`;
        lastLi.innerHTML = `<a class="page-link" href="#" onclick="event.preventDefault(); loadTaskList(${pagination.pages})" title="末页">
            <i class="fas fa-angle-double-right"></i>
        </a>`;
        paginationControls.appendChild(lastLi);
    }

    // 页码跳转功能
    function jumpToPage() {
        const pageJumpInput = document.getElementById('pageJumpInput');
        const targetPage = parseInt(pageJumpInput.value);
        const totalPagesSpan = document.getElementById('totalPagesSpan');
        const totalPages = parseInt(totalPagesSpan.textContent);

        if (isNaN(targetPage) || targetPage < 1 || targetPage > totalPages) {
            showNotification(`请输入有效的页码 (1-${totalPages})`, 'warning');
            return;
        }

        loadTaskList(targetPage);
        pageJumpInput.value = '';
    }

    // 更新WebSocket连接状态指示器
    function updateWebSocketStatus(status) {
        const statusElement = document.getElementById('websocketStatus');
        if (!statusElement) return;

        switch(status) {
            case 'connected':
                statusElement.className = 'badge bg-success';
                statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>已连接';
                statusElement.title = 'WebSocket连接正常';
                break;
            case 'connecting':
                statusElement.className = 'badge bg-warning';
                statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>连接中...';
                statusElement.title = 'WebSocket正在连接';
                break;
            case 'disconnected':
                statusElement.className = 'badge bg-secondary';
                statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>已断开';
                statusElement.title = 'WebSocket连接已断开';
                break;
            case 'error':
                statusElement.className = 'badge bg-danger';
                statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>连接失败';
                statusElement.title = 'WebSocket连接失败';
                break;
            default:
                statusElement.className = 'badge bg-secondary';
                statusElement.innerHTML = '<i class="fas fa-circle me-1"></i>未知状态';
                statusElement.title = 'WebSocket状态未知';
        }
    }

    // WebSocket连接初始化函数
    function initializeWebSocket() {
        try {
            console.log("初始化WebSocket连接...");

            // 设置初始连接状态
            updateWebSocketStatus('connecting');

            // 创建Socket.IO连接
            socket = io(window.location.origin, {
                reconnection: true,
                reconnectionDelay: 1000,
                reconnectionDelayMax: 5000,
                reconnectionAttempts: maxReconnectAttempts
            });
            

            // 连接成功事件
            socket.on('connect', function() {
                console.log('WebSocket连接成功，Socket ID:', socket.id);
                reconnectAttempts = 0;
                console.log('注册的事件监听器:', socket._callbacks);

                // 更新连接状态指示器
                updateWebSocketStatus('connected');

                // 显示连接成功通知
                showNotification('WebSocket连接成功', 'success');
            });
            
            // 连接错误事件
            socket.on('connect_error', function(error) {
                console.error('WebSocket连接错误:', error);
                reconnectAttempts++;

                // 更新连接状态指示器
                updateWebSocketStatus('error');

                if (reconnectAttempts >= maxReconnectAttempts) {
                    console.error('WebSocket重连次数超过限制，请刷新页面重试');
                    showNotification('WebSocket连接失败，请刷新页面重试', 'error');
                } else {
                    showNotification(`WebSocket连接失败，正在重试... (${reconnectAttempts}/${maxReconnectAttempts})`, 'warning');
                }
            });

            socket.on('disconnect', function(reason) {
                console.log('WebSocket断开连接，原因:', reason);

                // 更新连接状态指示器
                updateWebSocketStatus('disconnected');
            });
            
            // 监听任务状态更新
            socket.on('ota_task_update', function(data) {
                console.log("收到任务状态更新:", data);

                // 验证数据格式
                if (!data || typeof data !== 'object') {
                    console.error("收到的数据格式不正确:", data);
                    return;
                }

                // 确保必要字段存在
                if (!data.task_id) {
                    console.error("任务ID缺失:", data);
                    return;
                }

                // 标准化数据格式
                const normalizedData = {
                    task_id: String(data.task_id),
                    status: data.status || '未知',
                    progress: parseInt(data.progress) || 0,
                    message: data.message || '',
                    error_message: data.error_message || '',
                    timestamp: data.timestamp || new Date().toISOString()
                };

                // 更新任务状态
                updateTaskStatus(normalizedData);

                // 显示实时更新通知（仅用于调试）
                if (window.location.search.includes('debug=1')) {
                    showNotification(`任务 ${normalizedData.task_id} 状态更新: ${normalizedData.status} ${normalizedData.progress}%`, 'info');
                }
            });

            socket.onAny((eventName, ...args) => {
                console.log('收到事件:', eventName, args);
            });
        } catch (error) {
            console.error('初始化WebSocket失败:', error);
        }
    }
    
    // 更新任务状态的函数
    function updateTaskStatus(data) {
        const taskRow = document.querySelector(`tr[data-task-id="${data.task_id}"]`);
        if (taskRow) {
            console.log("找到任务行，更新状态");
            // 更新状态
            const statusCell = taskRow.querySelector('td:nth-child(4)');
            const progressBar = taskRow.querySelector('.progress-bar');
            
            // 更新状态标签
            let statusHtml = '';
            if (data.status === '成功') {
                statusHtml = '<span class="badge bg-success"><i class="fas fa-check-circle me-1"></i>成功</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-success');
                
                console.log("任务成功");
            } else if (data.status === '失败') {
                statusHtml = '<span class="badge bg-danger"><i class="fas fa-times-circle me-1"></i>失败</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-danger');
                
                console.log("任务失败，添加重试按钮");
                // 为失败的任务添加重试按钮
                const actionCell = taskRow.querySelector('td:nth-child(8)');
                if (actionCell && !actionCell.querySelector('.btn-warning')) {
                    const btnGroup = actionCell.querySelector('.btn-group');
                    if (btnGroup) {
                        const retryBtn = document.createElement('button');
                        retryBtn.type = 'button';
                        retryBtn.className = 'btn btn-sm btn-warning';
                        retryBtn.title = '重试';
                        retryBtn.innerHTML = '<i class="fas fa-redo"></i>';
                        retryBtn.onclick = function() { retryTask(data.task_id); };
                        
                        // 插入到第二个位置
                        if (btnGroup.children.length > 1) {
                            btnGroup.insertBefore(retryBtn, btnGroup.children[1]);
                        } else {
                            btnGroup.appendChild(retryBtn);
                        }
                    }
                }
            } else if (data.status === '等待中') {
                statusHtml = '<span class="badge bg-secondary"><i class="fas fa-hourglass-half me-1"></i>等待中</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-secondary');
            } else if (data.status === '已暂停') {
                statusHtml = '<span class="badge bg-warning"><i class="fas fa-pause me-1"></i>已暂停</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-warning');
            } else if (data.status === '已取消') {
                statusHtml = '<span class="badge bg-dark"><i class="fas fa-ban me-1"></i>已取消</span>';
                progressBar.classList.remove('progress-bar-animated');
                progressBar.classList.add('bg-dark');
            } else {
                // 如果已经添加了动画效果，则不添加
                statusHtml = '<span class="badge bg-warning"><i class="fas fa-sync fa-spin me-1"></i>进行中</span>';
                progressBar.classList.add('progress-bar-animated', 'progress-bar-striped');
                // if (!progressBar.classList.contains('progress-bar-animated')) {
                // }
                console.log("任务进行中");
            }
            statusCell.innerHTML = statusHtml;
            
            // 更新进度条 - 添加平滑过渡效果
            const currentProgress = parseInt(progressBar.getAttribute('aria-valuenow') || 0);
            const targetProgress = parseInt(data.progress) || 0;

            // 确保进度值在有效范围内
            const validProgress = Math.max(0, Math.min(100, targetProgress));

            // 如果进度有变化，添加过渡效果
            if (currentProgress !== validProgress) {
                // 添加过渡效果
                progressBar.style.transition = 'width 0.5s ease-in-out';

                // 设置新的进度值
                progressBar.style.width = `${validProgress}%`;
                progressBar.setAttribute('aria-valuenow', validProgress);
                progressBar.textContent = `${validProgress}%`;

                // 记录进度变化
                console.log(`进度从 ${currentProgress}% 更新到 ${validProgress}%`);

                // 如果进度达到100%，确保进度条样式正确
                if (validProgress === 100 && data.status === '成功') {
                    progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                    progressBar.classList.add('bg-success');
                }
            }
            
            // 如果任务完成，显示通知
            if (['成功', '失败', '已取消', '已暂停'].includes(data.status)) {
                console.log("任务状态变更，显示通知");
                let message, type;
                switch(data.status) {
                    case '成功':
                        message = '任务执行成功';
                        type = 'success';
                        break;
                    case '失败':
                        message = '任务执行失败';
                        type = 'error';
                        break;
                    case '已取消':
                        message = '任务已取消';
                        type = 'warning';
                        break;
                    case '已暂停':
                        message = '任务已暂停';
                        type = 'info';
                        break;
                }
                showNotification(message, type);
            }
        } else {
            console.log("未找到任务行");
        }
    }
    
    // 确保DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM已加载完成，开始初始化...");

        // 初始化通知组件
        const notificationToastEl = document.getElementById('notificationToast');
        if (notificationToastEl) {
            notificationToast = new bootstrap.Toast(notificationToastEl);
        }

        // 初始化模态框
        const taskDetailsModalEl = document.getElementById('taskDetailsModal');
        if (taskDetailsModalEl) {
            taskDetailsModal = new bootstrap.Modal(taskDetailsModalEl);
            console.log("模态框初始化成功");
        } else {
            console.error("无法找到模态框元素");
        }

        // 设置事件监听器
        setupEventListeners();

        // 初始化WebSocket连接
        initializeWebSocket();

        // 加载任务列表
        loadTaskList(1);
    });

    // 检查是否需要重定向到登录页面
    function checkLoginRedirect(response) {
        // 如果返回了HTML而不是JSON，那么可能是登录页面
        if (response.headers.get('Content-Type').includes('text/html')) {
            console.log("收到HTML响应，可能是登录页面");
            alert("您的登录已过期，将跳转到登录页面");
            window.location.href = '/login';
            return true;
        }
        return false;
    }

    // 查看任务详情
    window.viewTaskDetails = function(taskId) {
        console.log("查看任务详情被点击，任务ID:", taskId);
        
        // 如果模态框还没初始化，则初始化它
        if (!taskDetailsModal) {
            const taskDetailsModalEl = document.getElementById('taskDetailsModal');
            if (!taskDetailsModalEl) {
                console.error("无法找到任务详情模态框元素");
                return;
            }
            taskDetailsModal = new bootstrap.Modal(taskDetailsModalEl);
        }
        
        // 显示加载状态
        document.getElementById('detailTaskId').textContent = '加载中...';
        document.getElementById('detailDeviceId').textContent = '加载中...';
        document.getElementById('detailFirmwareVersion').textContent = '加载中...';
        document.getElementById('detailFirmwareCrc').textContent = '加载中...';
        document.getElementById('detailStatus').textContent = '加载中...';
        document.getElementById('detailProgress').textContent = '0';
        document.getElementById('detailCreatedAt').textContent = '加载中...';
        document.getElementById('detailError').textContent = '加载中...';
        
        // 显示模态框
        taskDetailsModal.show();
        
        // 构建正确的URL
        const url = `/ota/task/${taskId}`;
        console.log("正在发送请求到:", url);
        
        // 获取任务详情数据
        fetch(url)
            .then(response => {
                console.log("收到响应:", response.status, response.headers.get('Content-Type'));
                
                // 检查是否需要重定向到登录页面
                if (checkLoginRedirect(response)) {
                    return Promise.reject(new Error('需要登录'));
                }
                
                if (!response.ok) {
                    throw new Error(`网络响应错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("获取到的任务数据:", data);
                document.getElementById('detailTaskId').textContent = data.id;
                document.getElementById('detailDeviceId').textContent = data.device_name;
                document.getElementById('detailFirmwareVersion').textContent = data.firmware_version;

                // 设置CRC信息
                const crcElement = document.getElementById('detailFirmwareCrc');
                if (data.firmware_crc) {
                    crcElement.textContent = data.firmware_crc;
                    crcElement.className = 'firmware-crc';
                } else {
                    crcElement.textContent = 'CRC未知';
                    crcElement.className = 'firmware-crc unknown';
                }

                document.getElementById('detailStatus').textContent = data.status;
                document.getElementById('detailProgress').textContent = data.progress;
                document.getElementById('detailCreatedAt').textContent = data.created_at;
                document.getElementById('detailError').textContent = data.error_message || '无';
            })
            .catch(error => {
                console.error('获取任务详情失败:', error);
                if (error.message !== '需要登录') {
                    alert('获取任务详情失败: ' + error.message);
                    // 关闭模态框
                    taskDetailsModal.hide();
                }
            });
    };
    
    // 通用任务操作函数
    async function performTaskAction(taskId, action, confirmMessage, successMessage) {
        console.log(`${action}任务被点击，任务ID:`, taskId);

        if (confirm(confirmMessage)) {
            try {
                const url = `/ota/task/${taskId}/${action}`;
                console.log(`正在发送POST请求到:`, url);

                const data = await Utils.apiRequest(url, { method: 'POST' });

                console.log(`${action}任务响应:`, data);
                if (data.success) {
                    showNotification(successMessage, 'success');
                    loadTaskList(currentPage);
                } else {
                    showNotification(data.message || `${action}失败`, 'error');
                }
            } catch (error) {
                console.error(`${action}任务失败:`, error);
                if (error.message !== '需要登录') {
                    showNotification(`${action}任务失败: ` + error.message, 'error');
                }
            }
        }
    }

    // 重试任务
    window.retryTask = function(taskId) {
        performTaskAction(taskId, 'retry', '确定要重试此任务吗？', '任务已重新开始');
    };

    // 删除任务
    window.deleteTask = async function(taskId) {
        console.log("删除任务被点击，任务ID:", taskId);

        if (confirm('确定要删除此任务吗？')) {
            try {
                const url = `/api/ota/task/delete/${taskId}`;
                console.log("正在发送DELETE请求到:", url);

                const data = await Utils.apiRequest(url, { method: 'DELETE' });

                console.log("删除任务响应:", data);
                if (data.success) {
                    showNotification('任务删除成功', 'success');
                    if (data.warning) {
                        showNotification(data.warning, 'warning');
                    }
                    loadTaskList(currentPage);
                } else {
                    showNotification(data.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除任务失败:', error);
                if (error.message !== '需要登录') {
                    showNotification('删除任务失败: ' + error.message, 'error');
                }
            }
        }
    };

    // 暂停任务
    window.pauseTask = function(taskId) {
        performTaskAction(taskId, 'pause', '确定要暂停此任务吗？', '任务暂停请求已发送');
    };

    // 恢复任务
    window.resumeTask = function(taskId) {
        performTaskAction(taskId, 'resume', '确定要恢复此任务吗？', '任务恢复请求已发送');
    };

    // 取消任务
    window.cancelTask = function(taskId) {
        performTaskAction(taskId, 'cancel', '确定要取消此任务吗？此操作不可撤销。', '任务已取消');
    };

    // 测试WebSocket连接
    window.testWebSocket = async function() {
        console.log("测试WebSocket连接被点击");

        // 显示测试开始通知
        showNotification('正在测试WebSocket连接...', 'info');

        try {
            const data = await Utils.apiRequest('/api/websocket/test', { method: 'GET' });

            console.log("WebSocket测试响应:", data);
            if (data.success) {
                const status = data.status;
                let message = `WebSocket测试完成:\n`;
                message += `- SocketIO状态: ${status.socketio_status ? '正常' : '异常'}\n`;
                message += `- 测试消息发送: ${status.test_message_sent ? '成功' : '失败'}\n`;
                message += `- 直接消息发送: ${status.direct_message_sent ? '成功' : '失败'}\n`;
                message += `- 测试任务ID: ${status.test_task_ids.join(', ')}`;

                showNotification(message, 'success');

                // 如果有测试任务ID，尝试在表格中高亮显示
                if (status.test_task_ids && status.test_task_ids.length > 0) {
                    setTimeout(() => {
                        status.test_task_ids.forEach(testId => {
                            const testRow = document.querySelector(`tr[data-task-id="${testId}"]`);
                            if (testRow) {
                                testRow.style.backgroundColor = '#fff3cd';
                                setTimeout(() => {
                                    testRow.style.backgroundColor = '';
                                }, 3000);
                            }
                        });
                    }, 1000);
                }
            } else {
                showNotification('WebSocket测试失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('WebSocket测试失败:', error);
            if (error.message !== '需要登录') {
                showNotification('WebSocket测试失败: ' + error.message, 'error');
            }
        }
    };

    console.log("OTA任务脚本加载完成");
</script>
{% endblock %}