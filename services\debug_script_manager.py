#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试脚本管理模块
用于管理设备调试脚本的执行
"""

import os
import sys
import time
import threading
import logging
from datetime import datetime
from typing import Optional, Any
from models.debug_script import DebugScript
from models.device import Device
from models.database import db
from services.time_series_service import time_series_service
from services.iot_client_manager import IoTClientManager
from iot_client.functions.register_manager import RegisterManager
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

class DebugScriptManager:
    """调试脚本管理类，管理设备调试脚本的执行"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """单例模式"""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(DebugScriptManager, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance

    def __init__(self):
        """初始化调试脚本管理器"""
        if self._initialized:
            return

        self._initialized = True
        self.script_threads = {}  # 设备ID -> 线程对象
        self.script_stop_events = {}  # 设备ID -> 停止事件

    def start_script(self, device_id: int, frequency: int) -> bool:
        """
        启动设备调试脚本

        Args:
            device_id: 设备ID
            frequency: 执行频率（秒）

        Returns:
            bool: 启动是否成功
        """
        try:
            # 检查设备是否存在
            device = Device.query.get(device_id)
            if not device:
                logger.error(f"设备不存在: {device_id}")
                return False

            # 检查是否已有运行中的脚本
            if device_id in self.script_threads and self.script_threads[device_id].is_alive():
                logger.warning(f"设备 {device_id} 的调试脚本已在运行中")
                return False

            # 创建或更新调试脚本记录
            script = DebugScript.query.filter_by(device_id=device_id).first()
            if not script:
                script = DebugScript(device_id=device_id)
                db.session.add(script)

            script.enabled = True
            script.frequency = frequency
            script.last_execution_status = "启动中"
            db.session.commit()

            # 创建停止事件
            stop_event = threading.Event()
            self.script_stop_events[device_id] = stop_event

            # 获取当前应用上下文
            from flask import current_app
            app = current_app._get_current_object()  # 获取实际的应用对象，而不是代理

            # 创建并启动线程
            thread = threading.Thread(
                target=self._run_script,
                args=(app, device_id, frequency, stop_event),
                daemon=True
            )
            self.script_threads[device_id] = thread
            thread.start()

            logger.info(f"设备 {device_id} 的调试脚本已启动，频率: {frequency}秒")
            return True

        except Exception as e:
            logger.error(f"启动调试脚本异常: {e}")
            return False

    def stop_script(self, device_id: int) -> bool:
        """
        停止设备调试脚本

        Args:
            device_id: 设备ID

        Returns:
            bool: 停止是否成功
        """
        try:
            # 检查是否有运行中的脚本
            if device_id not in self.script_threads or not self.script_threads[device_id].is_alive():
                logger.warning(f"设备 {device_id} 没有运行中的调试脚本")

                # 更新数据库状态
                script = DebugScript.query.filter_by(device_id=device_id).first()
                if script:
                    script.enabled = False
                    script.last_execution_status = "已停止"
                    db.session.commit()

                return True

            # 设置停止事件
            if device_id in self.script_stop_events:
                self.script_stop_events[device_id].set()

            # 等待线程结束
            self.script_threads[device_id].join(timeout=5)

            # 更新数据库状态
            script = DebugScript.query.filter_by(device_id=device_id).first()
            if script:
                script.enabled = False
                script.last_execution_status = "已停止"
                db.session.commit()

            logger.info(f"设备 {device_id} 的调试脚本已停止")
            return True

        except Exception as e:
            logger.error(f"停止调试脚本异常: {e}")
            return False

    def get_script_status(self, device_id: int) -> dict[str, Any]:
        """
        获取设备调试脚本状态

        Args:
            device_id: 设备ID

        Returns:
            dict: 脚本状态信息
        """
        try:
            # 从数据库获取脚本信息
            script = DebugScript.query.filter_by(device_id=device_id).first()
            if not script:
                return {
                    "enabled": False,
                    "frequency": 60,
                    "total_executions": 0,
                    "successful_executions": 0,
                    "last_execution_time": None,
                    "last_execution_status": "未启动",
                    "is_running": False
                }

            # 检查线程是否在运行
            is_running = (
                device_id in self.script_threads and
                self.script_threads[device_id].is_alive()
            )

            return {
                "enabled": script.enabled,
                "frequency": script.frequency,
                "total_executions": script.total_executions,
                "successful_executions": script.successful_executions,
                "last_execution_time": script.last_execution_time.isoformat() if script.last_execution_time else None,
                "last_execution_status": script.last_execution_status,
                "is_running": is_running
            }

        except Exception as e:
            logger.error(f"获取调试脚本状态异常: {e}")
            return {
                "error": str(e)
            }

    def is_script_running(self, device_id: int) -> bool:
        """
        检查指定设备的调试脚本是否正在运行

        Args:
            device_id: 设备ID

        Returns:
            bool: 脚本是否正在运行
        """
        try:
            # 检查线程是否存在且活跃
            if device_id in self.script_threads:
                thread = self.script_threads[device_id]
                return thread.is_alive()

            return False

        except Exception as e:
            logger.error(f"检查脚本运行状态异常: {e}")
            return False

    def _run_script(self, app, device_id: int, frequency: int, stop_event: threading.Event):
        """
        运行调试脚本

        Args:
            app: Flask应用对象
            device_id: 设备ID
            frequency: 执行频率（秒）
            stop_event: 停止事件
        """
        try:
            with app.app_context():
                # 获取设备信息
                device = Device.query.get(device_id)
                if not device:
                    logger.error(f"设备不存在: {device_id}")
                    return

                # 获取脚本记录
                script = DebugScript.query.filter_by(device_id=device_id).first()
                if not script:
                    logger.error(f"设备 {device_id} 的调试脚本记录不存在")
                    return

                # 循环执行脚本
                while not stop_event.is_set():
                    try:
                        # 执行调试信息查询
                        success = self._query_debug_info(device)

                        # 更新执行状态
                        script.total_executions += 1
                        if success:
                            script.successful_executions += 1

                        script.last_execution_time = datetime.now()
                        script.last_execution_status = "成功" if success else "失败"
                        db.session.commit()

                    except Exception as e:
                        logger.error(f"执行调试脚本异常: {e}")

                        # 更新执行状态
                        script.total_executions += 1
                        script.last_execution_time = datetime.now()
                        script.last_execution_status = f"异常: {str(e)}"
                        db.session.commit()

                    # 等待下一次执行
                    if stop_event.wait(timeout=frequency):
                        break

                # 更新脚本状态
                script.enabled = False
                script.last_execution_status = "已停止"
                db.session.commit()

        except Exception as e:
            logger.error(f"运行调试脚本线程异常: {e}")

    def _query_debug_info(self, device: Device) -> bool:
        """
        查询设备调试信息

        Args:
            device: 设备对象

        Returns:
            bool: 查询是否成功
        """
        try:
            # 检查IoT客户端是否已启动
            if not IoTClientManager.is_running():
                logger.error("IoT客户端未启动，无法查询调试信息")
                return False

            # 获取IoT客户端
            iot_client = IoTClientManager.get_instance()

            # 构建topic
            topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

            # 创建寄存器管理器
            reg_manager = RegisterManager(iot_client, topic_full_name, logger)

            # 查询设备调试信息
            msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 0)
            if not msg:
                logger.error(f"设备 {device.id} 调试信息查询失败")
                return False

            # 解析调试信息
            parsed_data = msg.get('parsed_data', {})
            result = parsed_data.get('result', 1)  # 默认为错误

            if result != 0:
                logger.error(f"设备 {device.id} 返回错误: {result}")
                return False

            info = parsed_data.get('info', {})
            if not info:
                logger.error(f"设备 {device.id} 返回的调试信息不包含info字段")
                return False

            # 提取BL0910 RMS寄存器值
            bl0910_rms_regs = info.get('bl0910_rms_regs', [])
            if not bl0910_rms_regs or not isinstance(bl0910_rms_regs, list):
                logger.error(f"设备 {device.id} 返回的调试信息不包含有效的BL0910 RMS寄存器值")
                return False

            # 计算功率值
            channel_powers = []
            for i, reg_value in enumerate(bl0910_rms_regs):
                if i < 10:  # 只处理前10个通道
                    # 使用与前端相同的计算方法
                    coef = [
                        [-0.0014, -0.8803],  # MEASURE_1
                        [-0.0014, -0.6832],  # MEASURE_2
                        [0.0014, -0.726],    # MEASURE_3
                        [0.0014, -0.6832],   # MEASURE_4
                        [0.0014, -0.6832],   # MEASURE_5
                        [0.00134339, -0.8044], # MEASURE_6
                        [0.00134339, -0.8044], # MEASURE_7
                        [-0.0014, -0.6832],  # MEASURE_8
                        [-0.0014, -0.6832],  # MEASURE_9
                        [0.0014, -0.8305]    # MEASURE_10
                    ]

                    # 将寄存器值视为int32_t整数
                    int32_value = reg_value & 0xFFFFFFFF
                    if int32_value & 0x80000000:
                        int32_value = int32_value - 0x100000000

                    # 应用线性变换: power = slope * register_value + intercept
                    power = coef[i][0] * int32_value + coef[i][1]
                    channel_powers.append(power)

            # 提取所有数据字段，没有的用None代替
            # 基本数据字段
            # 电压值，除以100转换为实际值
            voltage = None
            if info.get('voltage') is not None:
                voltage = info.get('voltage', 0) / 100.0

            # 温度值，除以100转换为实际值
            temperature = None
            if info.get('temperature') is not None:
                temperature = info.get('temperature', 0) / 100.0

            # 总有功功率，除以100转换为实际值
            total_power = None
            if info.get('total_power') is not None:
                total_power = info.get('total_power', 0) / 100.0

            csq = info.get('csq') # 信号质量
            ber = info.get('ber') # 误码率

            # 提取其他可能存在的字段
            bl0910_error_count = info.get('bl0910_error_count')
            relay_state = info.get('relay_state')
            relay_bits = info.get('relay_bits')
            short_period_error_count = info.get('short_period_error_count')
            long_period_error_count = info.get('long_period_error_count')
            zero_cross_time = info.get('zero_cross_time')

            # 写入时序数据库
            success = time_series_service.write_sensor_data(
                device.device_id,
                channel_powers,
                voltage,
                temperature,
                total_power,
                csq,
                ber,
                bl0910_error_count,
                relay_state,
                relay_bits,
                short_period_error_count,
                long_period_error_count,
                zero_cross_time
            )

            if not success:
                logger.error(f"设备 {device.id} 数据写入时序数据库失败")
                return False

            logger.info(f"设备 {device.id} 调试信息查询成功，已写入时序数据库")
            return True

        except Exception as e:
            logger.error(f"查询调试信息异常: {e}")
            return False

# 创建全局实例
debug_script_manager = DebugScriptManager()
