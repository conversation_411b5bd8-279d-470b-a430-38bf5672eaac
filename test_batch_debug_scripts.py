#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量调试脚本控制功能测试脚本
"""

import requests
import json
import time
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:5000"
TEST_DEVICE_IDS = [1, 2, 3]  # 替换为实际的设备ID

def test_get_device_info():
    """测试获取设备信息API"""
    print("=" * 50)
    print("测试获取设备信息API")
    print("=" * 50)
    
    for device_id in TEST_DEVICE_IDS:
        try:
            response = requests.get(f"{BASE_URL}/api/device/{device_id}")
            print(f"设备 {device_id}: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    device_info = data.get('device', {})
                    print(f"  设备ID: {device_info.get('device_id')}")
                    print(f"  备注: {device_info.get('remark')}")
                    print(f"  产品密钥: {device_info.get('product_key')}")
                else:
                    print(f"  错误: {data.get('error')}")
            else:
                print(f"  HTTP错误: {response.text}")
                
        except Exception as e:
            print(f"  异常: {e}")
        
        print()

def test_debug_script_operations():
    """测试调试脚本启动/停止操作"""
    print("=" * 50)
    print("测试调试脚本操作")
    print("=" * 50)
    
    for device_id in TEST_DEVICE_IDS:
        print(f"测试设备 {device_id}:")
        
        # 测试启动调试脚本
        try:
            print("  启动调试脚本...")
            response = requests.post(
                f"{BASE_URL}/debug_script/start/{device_id}",
                headers={'Content-Type': 'application/json'},
                data=json.dumps({'frequency': 60})
            )
            
            print(f"    状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"    结果: {'成功' if data.get('success') else '失败'}")
                if not data.get('success'):
                    print(f"    错误: {data.get('error', data.get('message'))}")
            else:
                print(f"    HTTP错误: {response.text}")
                
        except Exception as e:
            print(f"    异常: {e}")
        
        # 等待一下
        time.sleep(2)
        
        # 测试停止调试脚本
        try:
            print("  停止调试脚本...")
            response = requests.post(f"{BASE_URL}/debug_script/stop/{device_id}")
            
            print(f"    状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"    结果: {'成功' if data.get('success') else '失败'}")
                if not data.get('success'):
                    print(f"    错误: {data.get('error', data.get('message'))}")
            else:
                print(f"    HTTP错误: {response.text}")
                
        except Exception as e:
            print(f"    异常: {e}")
        
        print()

def test_batch_operations_simulation():
    """模拟批量操作测试"""
    print("=" * 50)
    print("模拟批量操作测试")
    print("=" * 50)
    
    print("模拟批量重启调试脚本:")
    success_count = 0
    failed_count = 0
    
    for device_id in TEST_DEVICE_IDS:
        try:
            # 模拟获取设备信息
            device_response = requests.get(f"{BASE_URL}/api/device/{device_id}")
            if device_response.status_code != 200:
                print(f"  设备 {device_id}: 获取设备信息失败")
                failed_count += 1
                continue
            
            device_data = device_response.json()
            if not device_data.get('success'):
                print(f"  设备 {device_id}: 设备不存在")
                failed_count += 1
                continue
            
            device_info = device_data.get('device', {})
            
            # 模拟启动调试脚本
            script_response = requests.post(
                f"{BASE_URL}/debug_script/start/{device_id}",
                headers={'Content-Type': 'application/json'},
                data=json.dumps({'frequency': 60})
            )
            
            if script_response.status_code == 200:
                script_data = script_response.json()
                if script_data.get('success'):
                    print(f"  设备 {device_info.get('device_id', device_id)}: 重启成功")
                    success_count += 1
                else:
                    print(f"  设备 {device_info.get('device_id', device_id)}: 重启失败 - {script_data.get('error', script_data.get('message'))}")
                    failed_count += 1
            else:
                print(f"  设备 {device_info.get('device_id', device_id)}: HTTP错误 - {script_response.status_code}")
                failed_count += 1
                
        except Exception as e:
            print(f"  设备 {device_id}: 异常 - {e}")
            failed_count += 1
    
    print(f"\n批量操作结果: 成功 {success_count} 个，失败 {failed_count} 个")

def main():
    """主测试函数"""
    print(f"批量调试脚本控制功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试服务器: {BASE_URL}")
    print(f"测试设备: {TEST_DEVICE_IDS}")
    print()
    
    # 测试1: 获取设备信息
    test_get_device_info()
    
    # 测试2: 调试脚本操作
    test_debug_script_operations()
    
    # 测试3: 模拟批量操作
    test_batch_operations_simulation()
    
    print("=" * 50)
    print("测试完成")
    print("=" * 50)
    
    print("\n前端测试步骤:")
    print("1. 访问设备管理页面")
    print("2. 选择一些设备（勾选复选框）")
    print("3. 点击'批量重启调试脚本'按钮")
    print("4. 查看操作结果模态框")
    print("5. 点击'批量停止调试脚本'按钮")
    print("6. 查看操作结果模态框")
    print("\n预期结果:")
    print("- 按钮点击后显示确认对话框")
    print("- 确认后显示操作结果模态框")
    print("- 模态框显示操作进度和结果统计")
    print("- 每个设备的操作结果显示在表格中")

if __name__ == "__main__":
    main()
