#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化时序数据库性能测试脚本
对比优化前后的性能差异
"""

import os
import sys
import time
import threading
import random
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from models.database import db
from models.optimized_time_series import OptimizedTimeSeriesData
from services.optimized_time_series_service import optimized_time_series_service
from config import Config
from utils.logger import LoggerManager

logger = LoggerManager.get_logger()


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app


class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.app = create_app()
        self.test_devices = [f"test_device_{i:03d}" for i in range(1, 51)]  # 50个测试设备
        self.test_duration = 60  # 测试持续时间（秒）
        self.write_interval = 1  # 写入间隔（秒）
        
    def generate_sensor_data(self):
        """生成模拟传感器数据"""
        return {
            'channel_powers': [random.uniform(0, 100) for _ in range(10)],
            'voltage': random.uniform(200, 240),
            'temperature': random.uniform(20, 40),
            'total_power': random.uniform(500, 1000),
            'csq': random.randint(0, 31),
            'ber': random.randint(0, 7),
            'bl0910_error_count': random.randint(0, 10),
            'short_period_error_count': random.randint(0, 5),
            'long_period_error_count': random.randint(0, 3),
            'relay_state': random.randint(0, 1),
            'relay_bits': random.randint(0, 255),
            'zero_cross_time': random.randint(1000, 2000)
        }
    
    def test_concurrent_writes(self, num_threads=10, writes_per_thread=100):
        """测试并发写入性能"""
        print(f"\n测试并发写入性能 ({num_threads} 线程, 每线程 {writes_per_thread} 次写入)")
        print("-" * 60)
        
        with self.app.app_context():
            start_time = time.time()
            success_count = 0
            error_count = 0
            
            def write_worker(thread_id):
                nonlocal success_count, error_count
                thread_success = 0
                thread_error = 0
                
                for i in range(writes_per_thread):
                    try:
                        device_id = random.choice(self.test_devices)
                        sensor_data = self.generate_sensor_data()
                        
                        success = optimized_time_series_service.write_sensor_data(
                            device_id, **sensor_data
                        )
                        
                        if success:
                            thread_success += 1
                        else:
                            thread_error += 1
                            
                    except Exception as e:
                        thread_error += 1
                        logger.error(f"写入异常 (线程{thread_id}): {e}")
                
                return thread_success, thread_error
            
            # 使用线程池执行并发写入
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(write_worker, i) for i in range(num_threads)]
                
                for future in as_completed(futures):
                    thread_success, thread_error = future.result()
                    success_count += thread_success
                    error_count += thread_error
            
            end_time = time.time()
            duration = end_time - start_time
            total_writes = num_threads * writes_per_thread
            
            print(f"总写入次数: {total_writes}")
            print(f"成功写入: {success_count}")
            print(f"失败写入: {error_count}")
            print(f"总耗时: {duration:.2f} 秒")
            print(f"写入速率: {success_count / duration:.2f} 次/秒")
            print(f"成功率: {success_count / total_writes * 100:.2f}%")
            
            return {
                'total_writes': total_writes,
                'success_count': success_count,
                'error_count': error_count,
                'duration': duration,
                'write_rate': success_count / duration,
                'success_rate': success_count / total_writes
            }
    
    def test_query_performance(self, num_queries=100):
        """测试查询性能"""
        print(f"\n测试查询性能 ({num_queries} 次查询)")
        print("-" * 60)
        
        with self.app.app_context():
            # 准备查询参数
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            query_types = [
                ('power', optimized_time_series_service.query_power_data),
                ('voltage', lambda d, s, e: optimized_time_series_service.query_single_value_data(d, 'voltage', s, e)),
                ('temperature', lambda d, s, e: optimized_time_series_service.query_single_value_data(d, 'temperature', s, e)),
                ('csq', optimized_time_series_service.query_csq_data),
            ]
            
            results = {}
            
            for query_name, query_func in query_types:
                print(f"测试 {query_name} 查询...")
                
                start_query_time = time.time()
                query_success = 0
                query_error = 0
                total_records = 0
                
                for i in range(num_queries):
                    try:
                        device_id = random.choice(self.test_devices)
                        data = query_func(device_id, start_time, end_time)
                        
                        if isinstance(data, dict):
                            # 功率数据返回字典
                            total_records += sum(len(channel_data) for channel_data in data.values())
                        elif isinstance(data, list):
                            # 其他数据返回列表
                            total_records += len(data)
                        
                        query_success += 1
                        
                    except Exception as e:
                        query_error += 1
                        logger.error(f"{query_name}查询异常: {e}")
                
                end_query_time = time.time()
                query_duration = end_query_time - start_query_time
                
                results[query_name] = {
                    'success_count': query_success,
                    'error_count': query_error,
                    'duration': query_duration,
                    'query_rate': query_success / query_duration,
                    'total_records': total_records,
                    'avg_records_per_query': total_records / max(query_success, 1)
                }
                
                print(f"  成功查询: {query_success}")
                print(f"  失败查询: {query_error}")
                print(f"  查询耗时: {query_duration:.2f} 秒")
                print(f"  查询速率: {query_success / query_duration:.2f} 次/秒")
                print(f"  返回记录: {total_records}")
                print(f"  平均记录/查询: {total_records / max(query_success, 1):.1f}")
                print()
            
            return results
    
    def test_memory_usage(self):
        """测试内存使用情况"""
        print("\n测试内存使用情况")
        print("-" * 60)
        
        try:
            import psutil
            process = psutil.Process()
            
            # 获取初始内存使用
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            print(f"初始内存使用: {initial_memory:.2f} MB")
            
            # 执行一些操作
            with self.app.app_context():
                # 写入一些数据
                for i in range(1000):
                    device_id = random.choice(self.test_devices)
                    sensor_data = self.generate_sensor_data()
                    optimized_time_series_service.write_sensor_data(device_id, **sensor_data)
                
                # 执行一些查询
                end_time = datetime.now()
                start_time = end_time - timedelta(hours=1)
                
                for i in range(100):
                    device_id = random.choice(self.test_devices)
                    optimized_time_series_service.query_power_data(device_id, start_time, end_time)
            
            # 获取最终内存使用
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            print(f"最终内存使用: {final_memory:.2f} MB")
            print(f"内存增长: {memory_increase:.2f} MB")
            
            # 获取缓存统计
            cache_stats = optimized_time_series_service.get_cache_stats()
            print(f"缓存大小: {cache_stats['cache_size']}/{cache_stats['max_cache_size']}")
            print(f"缓冲区大小: {cache_stats['buffer_size']}/{cache_stats['max_buffer_size']}")
            
            return {
                'initial_memory': initial_memory,
                'final_memory': final_memory,
                'memory_increase': memory_increase,
                'cache_stats': cache_stats
            }
            
        except ImportError:
            print("需要安装 psutil 库来测试内存使用")
            return None
    
    def run_full_test(self):
        """运行完整的性能测试"""
        print("=" * 60)
        print("优化时序数据库性能测试")
        print("=" * 60)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试设备数量: {len(self.test_devices)}")
        print()
        
        # 1. 并发写入测试
        write_results = self.test_concurrent_writes(num_threads=10, writes_per_thread=100)
        
        # 2. 查询性能测试
        query_results = self.test_query_performance(num_queries=50)
        
        # 3. 内存使用测试
        memory_results = self.test_memory_usage()
        
        # 4. 生成性能报告
        self.generate_performance_report(write_results, query_results, memory_results)
    
    def generate_performance_report(self, write_results, query_results, memory_results):
        """生成性能报告"""
        print("\n" + "=" * 60)
        print("性能测试报告")
        print("=" * 60)
        
        print("\n📊 写入性能:")
        print(f"  写入速率: {write_results['write_rate']:.2f} 次/秒")
        print(f"  成功率: {write_results['success_rate']*100:.2f}%")
        
        print("\n📊 查询性能:")
        for query_type, results in query_results.items():
            print(f"  {query_type} 查询速率: {results['query_rate']:.2f} 次/秒")
        
        if memory_results:
            print("\n📊 内存使用:")
            print(f"  内存增长: {memory_results['memory_increase']:.2f} MB")
            print(f"  缓存使用率: {memory_results['cache_stats']['cache_size']}/{memory_results['cache_stats']['max_cache_size']}")
        
        print("\n🎯 性能评估:")
        if write_results['write_rate'] > 100:
            print("  ✅ 写入性能优秀 (>100 次/秒)")
        elif write_results['write_rate'] > 50:
            print("  ⚠️  写入性能良好 (50-100 次/秒)")
        else:
            print("  ❌ 写入性能需要优化 (<50 次/秒)")
        
        avg_query_rate = sum(r['query_rate'] for r in query_results.values()) / len(query_results)
        if avg_query_rate > 50:
            print("  ✅ 查询性能优秀 (>50 次/秒)")
        elif avg_query_rate > 20:
            print("  ⚠️  查询性能良好 (20-50 次/秒)")
        else:
            print("  ❌ 查询性能需要优化 (<20 次/秒)")
        
        if memory_results and memory_results['memory_increase'] < 50:
            print("  ✅ 内存使用优秀 (<50 MB增长)")
        elif memory_results and memory_results['memory_increase'] < 100:
            print("  ⚠️  内存使用良好 (50-100 MB增长)")
        else:
            print("  ❌ 内存使用需要优化 (>100 MB增长)")


def main():
    """主函数"""
    # 设置环境
    os.environ['FLASK_ENV'] = 'development'
    
    # 运行性能测试
    test = PerformanceTest()
    test.run_full_test()


if __name__ == '__main__':
    main()
