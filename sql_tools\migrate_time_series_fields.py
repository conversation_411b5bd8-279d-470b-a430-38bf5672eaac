#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时序数据字段迁移脚本
将string_value字段从VARCHAR(255)改为TEXT类型
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models.database import db
from config import Config
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 初始化数据库
    db.init_app(app)
    
    return app


def migrate_string_value_field():
    """迁移string_value字段类型"""
    try:
        app = create_app()
        
        with app.app_context():
            logger.info("开始迁移时序数据字段类型...")
            
            # 获取当前环境
            env = os.environ.get('FLASK_ENV', 'production')
            table_prefix = 'dev_' if env == 'development' else 'prod_'
            
            # 检查表是否存在
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            tables_to_migrate = [
                f'{table_prefix}time_series_data',
                f'time_series_data'  # 也检查没有前缀的表
            ]
            
            migrated_tables = []
            
            for table_name in tables_to_migrate:
                if table_name in existing_tables:
                    try:
                        # 检查当前字段类型
                        columns = inspector.get_columns(table_name)
                        string_value_column = None
                        
                        for column in columns:
                            if column['name'] == 'string_value':
                                string_value_column = column
                                break
                        
                        if string_value_column:
                            current_type = str(string_value_column['type'])
                            logger.info(f"表 {table_name} 的 string_value 字段当前类型: {current_type}")
                            
                            # 如果是VARCHAR类型，则需要迁移
                            if 'VARCHAR' in current_type.upper() or 'CHARACTER VARYING' in current_type.upper():
                                logger.info(f"开始迁移表 {table_name} 的 string_value 字段...")
                                
                                # 执行ALTER TABLE语句
                                alter_sql = f"ALTER TABLE {table_name} ALTER COLUMN string_value TYPE TEXT;"
                                db.session.execute(alter_sql)
                                db.session.commit()
                                
                                logger.info(f"成功迁移表 {table_name} 的 string_value 字段为 TEXT 类型")
                                migrated_tables.append(table_name)
                            else:
                                logger.info(f"表 {table_name} 的 string_value 字段已经是正确类型，无需迁移")
                        else:
                            logger.warning(f"表 {table_name} 中未找到 string_value 字段")
                            
                    except Exception as e:
                        logger.error(f"迁移表 {table_name} 失败: {e}")
                        db.session.rollback()
                        continue
            
            if migrated_tables:
                logger.info(f"字段迁移完成，共迁移了 {len(migrated_tables)} 个表: {', '.join(migrated_tables)}")
            else:
                logger.info("没有需要迁移的表")
            
            return True
            
    except Exception as e:
        logger.error(f"字段迁移失败: {e}")
        return False


def verify_migration():
    """验证迁移结果"""
    try:
        app = create_app()
        
        with app.app_context():
            logger.info("验证字段迁移结果...")
            
            # 获取当前环境
            env = os.environ.get('FLASK_ENV', 'production')
            table_prefix = 'dev_' if env == 'development' else 'prod_'
            
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            tables_to_check = [
                f'{table_prefix}time_series_data',
                f'time_series_data'
            ]
            
            verification_passed = True
            
            for table_name in tables_to_check:
                if table_name in existing_tables:
                    columns = inspector.get_columns(table_name)
                    
                    for column in columns:
                        if column['name'] == 'string_value':
                            current_type = str(column['type'])
                            logger.info(f"表 {table_name} 的 string_value 字段类型: {current_type}")
                            
                            if 'TEXT' in current_type.upper():
                                logger.info(f"✓ 表 {table_name} 的字段类型验证通过")
                            else:
                                logger.error(f"✗ 表 {table_name} 的字段类型验证失败: {current_type}")
                                verification_passed = False
                            break
            
            return verification_passed
            
    except Exception as e:
        logger.error(f"验证迁移结果失败: {e}")
        return False


def backup_data():
    """备份数据（可选）"""
    try:
        app = create_app()
        
        with app.app_context():
            logger.info("开始备份时序数据...")
            
            # 获取当前环境
            env = os.environ.get('FLASK_ENV', 'production')
            table_prefix = 'dev_' if env == 'development' else 'prod_'
            
            # 检查数据量
            count_sql = f"SELECT COUNT(*) FROM {table_prefix}time_series_data;"
            result = db.session.execute(count_sql).scalar()
            
            logger.info(f"当前时序数据表中有 {result} 条记录")
            
            if result > 0:
                # 创建备份表
                backup_table_name = f"{table_prefix}time_series_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                backup_sql = f"""
                CREATE TABLE {backup_table_name} AS 
                SELECT * FROM {table_prefix}time_series_data;
                """
                
                db.session.execute(backup_sql)
                db.session.commit()
                
                logger.info(f"数据备份完成，备份表名: {backup_table_name}")
                return backup_table_name
            else:
                logger.info("没有数据需要备份")
                return None
            
    except Exception as e:
        logger.error(f"数据备份失败: {e}")
        return None


def main():
    """主函数"""
    print("=" * 60)
    print("时序数据字段迁移脚本")
    print("=" * 60)
    
    # 设置环境变量
    env = input("请选择环境 (development/production) [development]: ").strip()
    if not env:
        env = 'development'
    
    os.environ['FLASK_ENV'] = env
    print(f"当前环境: {env}")
    
    # 询问是否备份数据
    backup_choice = input("是否备份现有数据？(y/n) [y]: ").strip().lower()
    if backup_choice != 'n':
        print("\n1. 备份现有数据...")
        backup_table = backup_data()
        if backup_table:
            print(f"✓ 数据备份完成: {backup_table}")
        else:
            print("✓ 无需备份或备份失败")
    
    # 执行迁移
    print("\n2. 迁移字段类型...")
    if migrate_string_value_field():
        print("✓ 字段迁移成功")
    else:
        print("✗ 字段迁移失败")
        return False
    
    # 验证迁移结果
    print("\n3. 验证迁移结果...")
    if verify_migration():
        print("✓ 迁移验证成功")
    else:
        print("✗ 迁移验证失败")
        return False
    
    print("\n" + "=" * 60)
    print("字段迁移完成！")
    print("=" * 60)
    
    print("\n下一步操作：")
    print("1. 重启应用服务")
    print("2. 测试数据写入功能")
    print("3. 验证长字符串数据是否能正常存储")
    
    return True


if __name__ == '__main__':
    main()
