#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试脚本自动重启服务
在系统启动时自动重启已启用的调试脚本
"""

import os
import time
import threading
import logging
from datetime import datetime
from typing import List, Dict, Any
from flask import current_app

from models.debug_script import DebugScript
from models.device import Device
from models.database import db
from services.debug_script_manager import debug_script_manager
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class AutoRestartService:
    """调试脚本自动重启服务类"""
    
    def __init__(self):
        self.restart_delay = 5  # 重启延迟（秒）
        self.max_retry_count = 3  # 最大重试次数
        self.retry_interval = 10  # 重试间隔（秒）
        self.startup_log_file = 'logs/auto_restart.log'
        self.is_running = False
        
    def start_auto_restart(self, app_context) -> bool:
        """
        启动自动重启服务
        
        Args:
            app_context: Flask应用上下文
            
        Returns:
            bool: 启动是否成功
        """
        try:
            if self.is_running:
                logger.warning("自动重启服务已在运行")
                return True
            
            self.is_running = True
            
            # 在单独线程中执行重启逻辑
            restart_thread = threading.Thread(
                target=self._execute_auto_restart,
                args=(app_context,),
                daemon=True,
                name="AutoRestartThread"
            )
            restart_thread.start()
            
            logger.info("调试脚本自动重启服务已启动")
            return True
            
        except Exception as e:
            logger.error(f"启动自动重启服务失败: {e}")
            self.is_running = False
            return False
    
    def _execute_auto_restart(self, app_context):
        """
        执行自动重启逻辑
        
        Args:
            app_context: Flask应用上下文
        """
        try:
            # 等待系统完全启动
            time.sleep(self.restart_delay)
            
            with app_context:
                logger.info("开始执行调试脚本自动重启...")
                
                # 记录启动日志
                self._log_startup_event("开始自动重启检查")
                
                # 获取需要重启的脚本
                scripts_to_restart = self._get_scripts_to_restart()
                
                if not scripts_to_restart:
                    logger.info("没有需要重启的调试脚本")
                    self._log_startup_event("没有需要重启的调试脚本")
                    return
                
                logger.info(f"找到 {len(scripts_to_restart)} 个需要重启的调试脚本")
                self._log_startup_event(f"找到 {len(scripts_to_restart)} 个需要重启的调试脚本")
                
                # 逐个重启脚本
                success_count = 0
                failed_count = 0
                
                for script_info in scripts_to_restart:
                    try:
                        success = self._restart_single_script(script_info)
                        if success:
                            success_count += 1
                            self._log_startup_event(
                                f"成功重启设备 {script_info['device_id']} 的调试脚本"
                            )
                        else:
                            failed_count += 1
                            self._log_startup_event(
                                f"重启设备 {script_info['device_id']} 的调试脚本失败"
                            )
                            
                        # 重启间隔
                        time.sleep(2)
                        
                    except Exception as e:
                        failed_count += 1
                        error_msg = f"重启设备 {script_info['device_id']} 的调试脚本异常: {e}"
                        logger.error(error_msg)
                        self._log_startup_event(error_msg)
                
                # 记录总结
                summary = f"自动重启完成: 成功 {success_count} 个, 失败 {failed_count} 个"
                logger.info(summary)
                self._log_startup_event(summary)
                
        except Exception as e:
            error_msg = f"执行自动重启异常: {e}"
            logger.error(error_msg)
            self._log_startup_event(error_msg)
        finally:
            self.is_running = False
    
    def _get_scripts_to_restart(self) -> List[Dict[str, Any]]:
        """
        获取需要重启的调试脚本列表
        
        Returns:
            List: 需要重启的脚本信息列表
        """
        try:
            # 查询所有启用的调试脚本
            enabled_scripts = db.session.query(DebugScript).filter(
                DebugScript.enabled == True
            ).all()
            
            scripts_to_restart = []
            
            for script in enabled_scripts:
                # 获取设备信息
                device = db.session.query(Device).filter(
                    Device.id == script.device_id
                ).first()
                
                if not device:
                    logger.warning(f"调试脚本 {script.id} 对应的设备不存在")
                    continue
                
                # 检查脚本是否真的在运行
                is_actually_running = debug_script_manager.is_script_running(script.device_id)
                
                if not is_actually_running:
                    scripts_to_restart.append({
                        'script_id': script.id,
                        'device_id': script.device_id,
                        'device_name': device.device_id,
                        'frequency': script.frequency,
                        'last_execution_time': script.last_execution_time,
                        'last_execution_status': script.last_execution_status
                    })
                    
                    logger.info(f"发现需要重启的脚本: 设备 {device.device_id}, 频率 {script.frequency}秒")
            
            return scripts_to_restart
            
        except Exception as e:
            logger.error(f"获取需要重启的脚本列表失败: {e}")
            return []
    
    def _restart_single_script(self, script_info: Dict[str, Any]) -> bool:
        """
        重启单个调试脚本
        
        Args:
            script_info: 脚本信息字典
            
        Returns:
            bool: 重启是否成功
        """
        device_id = script_info['device_id']
        frequency = script_info['frequency']
        
        retry_count = 0
        
        while retry_count < self.max_retry_count:
            try:
                logger.info(f"尝试重启设备 {script_info['device_name']} 的调试脚本 (第 {retry_count + 1} 次)")
                
                # 确保脚本已停止
                if debug_script_manager.is_script_running(device_id):
                    debug_script_manager.stop_script(device_id)
                    time.sleep(2)
                
                # 启动脚本
                success = debug_script_manager.start_script(device_id, frequency)
                
                if success:
                    logger.info(f"成功重启设备 {script_info['device_name']} 的调试脚本")
                    
                    # 更新数据库状态
                    self._update_script_restart_status(script_info['script_id'], True)
                    
                    return True
                else:
                    retry_count += 1
                    if retry_count < self.max_retry_count:
                        logger.warning(f"重启失败，{self.retry_interval}秒后重试...")
                        time.sleep(self.retry_interval)
                    
            except Exception as e:
                retry_count += 1
                logger.error(f"重启设备 {script_info['device_name']} 的调试脚本异常: {e}")
                
                if retry_count < self.max_retry_count:
                    time.sleep(self.retry_interval)
        
        # 所有重试都失败
        logger.error(f"重启设备 {script_info['device_name']} 的调试脚本最终失败")
        self._update_script_restart_status(script_info['script_id'], False)
        
        return False
    
    def _update_script_restart_status(self, script_id: int, success: bool):
        """
        更新脚本重启状态
        
        Args:
            script_id: 脚本ID
            success: 重启是否成功
        """
        try:
            script = db.session.query(DebugScript).filter(
                DebugScript.id == script_id
            ).first()
            
            if script:
                if success:
                    script.last_execution_status = "自动重启成功"
                else:
                    script.last_execution_status = "自动重启失败"
                    script.enabled = False  # 重启失败时禁用脚本
                
                script.last_execution_time = datetime.now()
                db.session.commit()
                
        except Exception as e:
            logger.error(f"更新脚本重启状态失败: {e}")
            db.session.rollback()
    
    def _log_startup_event(self, message: str):
        """
        记录启动事件到专门的日志文件
        
        Args:
            message: 日志消息
        """
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(self.startup_log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            # 写入日志
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"[{timestamp}] {message}\n"
            
            with open(self.startup_log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
                
        except Exception as e:
            logger.error(f"写入启动日志失败: {e}")
    
    def get_restart_status(self) -> Dict[str, Any]:
        """
        获取自动重启状态
        
        Returns:
            Dict: 重启状态信息
        """
        try:
            # 读取最近的启动日志
            recent_logs = []
            if os.path.exists(self.startup_log_file):
                try:
                    with open(self.startup_log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        recent_logs = lines[-20:]  # 最近20条日志
                except Exception as e:
                    logger.warning(f"读取启动日志失败: {e}")
            
            # 获取当前运行的脚本数量
            running_scripts = db.session.query(DebugScript).filter(
                DebugScript.enabled == True
            ).count()
            
            return {
                'is_running': self.is_running,
                'running_scripts_count': running_scripts,
                'recent_logs': [log.strip() for log in recent_logs],
                'log_file': self.startup_log_file
            }
            
        except Exception as e:
            logger.error(f"获取重启状态失败: {e}")
            return {
                'is_running': self.is_running,
                'running_scripts_count': 0,
                'recent_logs': [],
                'log_file': self.startup_log_file,
                'error': str(e)
            }


# 创建全局实例
auto_restart_service = AutoRestartService()
