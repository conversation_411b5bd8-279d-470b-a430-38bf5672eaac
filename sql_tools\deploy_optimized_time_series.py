#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
部署优化时序数据库的完整脚本
包括删除旧表、创建新表、创建分区、索引等
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from models.database import db
from config import Config
from utils.logger import LoggerManager
from sqlalchemy import text

logger = LoggerManager.get_logger()


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    db.init_app(app)
    return app


def drop_old_tables(env_prefix):
    """删除旧的时序数据表"""
    try:
        logger.info(f"删除旧的时序数据表 ({env_prefix}环境)...")
        
        # 删除旧表
        old_tables = [
            f'{env_prefix}time_series_data',
            f'{env_prefix}time_series_batch',
            f'{env_prefix}time_series_buffer'
        ]
        
        for table_name in old_tables:
            try:
                drop_sql = text(f"DROP TABLE IF EXISTS {table_name} CASCADE;")
                db.session.execute(drop_sql)
                logger.info(f"删除表: {table_name}")
            except Exception as e:
                logger.warning(f"删除表 {table_name} 失败: {e}")
        
        db.session.commit()
        logger.info("旧表删除完成")
        return True
        
    except Exception as e:
        logger.error(f"删除旧表失败: {e}")
        db.session.rollback()
        return False


def create_optimized_tables(env_prefix):
    """创建优化的时序数据表"""
    try:
        logger.info(f"创建优化的时序数据表 ({env_prefix}环境)...")
        
        # 1. 创建主时序数据表（分区表）
        main_table_sql = f"""
        CREATE TABLE {env_prefix}time_series_data (
            id BIGSERIAL,
            device_id VARCHAR(50) NOT NULL,
            timestamp TIMESTAMPTZ NOT NULL,
            data_type SMALLINT NOT NULL,
            
            -- 优化的数据字段
            power_values REAL[10],
            voltage REAL,
            temperature REAL,
            total_power REAL,
            csq SMALLINT,
            ber SMALLINT,
            error_counts INTEGER[3],
            relay_state SMALLINT,
            relay_bits INTEGER,
            zero_cross_time INTEGER,
            
            created_at TIMESTAMPTZ DEFAULT NOW(),
            
            PRIMARY KEY (id, timestamp)
        ) PARTITION BY RANGE (timestamp);
        """
        
        db.session.execute(text(main_table_sql))
        logger.info(f"创建主表: {env_prefix}time_series_data")

        # 2. 创建缓冲表
        buffer_table_sql = f"""
        CREATE TABLE {env_prefix}time_series_buffer (
            device_id VARCHAR(50) NOT NULL,
            timestamp TIMESTAMPTZ NOT NULL,
            data_type SMALLINT NOT NULL,
            power_values REAL[10],
            voltage REAL,
            temperature REAL,
            total_power REAL,
            csq SMALLINT,
            ber SMALLINT,
            error_counts INTEGER[3],
            relay_state SMALLINT,
            relay_bits INTEGER,
            zero_cross_time INTEGER,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        """

        db.session.execute(text(buffer_table_sql))
        logger.info(f"创建缓冲表: {env_prefix}time_series_buffer")
        
        db.session.commit()
        return True
        
    except Exception as e:
        logger.error(f"创建优化表失败: {e}")
        db.session.rollback()
        return False


def create_partitions(env_prefix, months=12):
    """创建分区表"""
    try:
        logger.info(f"创建分区表 ({env_prefix}环境)...")
        
        # 创建分区函数
        partition_function_sql = f"""
        CREATE OR REPLACE FUNCTION create_monthly_partition_{env_prefix}(start_date date)
        RETURNS void AS $$
        DECLARE
            partition_name text;
            end_date date;
        BEGIN
            partition_name := '{env_prefix}time_series_data_' || to_char(start_date, 'YYYY_MM');
            end_date := start_date + interval '1 month';
            
            EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF {env_prefix}time_series_data 
                           FOR VALUES FROM (%L) TO (%L)',
                           partition_name, start_date, end_date);
                           
            -- 创建分区特定索引
            EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (device_id, timestamp DESC)',
                           'idx_' || partition_name || '_device_time', partition_name);
            EXECUTE format('CREATE INDEX IF NOT EXISTS %I ON %I (timestamp DESC, data_type)',
                           'idx_' || partition_name || '_time_type', partition_name);
        END;
        $$ LANGUAGE plpgsql;
        """
        
        db.session.execute(text(partition_function_sql))

        # 创建分区（过去6个月到未来6个月）
        start_date = datetime.now().replace(day=1) - timedelta(days=30 * 6)

        for i in range(months):
            current_date = start_date + timedelta(days=30 * i)
            current_date = current_date.replace(day=1)  # 月初

            partition_sql = text(f"SELECT create_monthly_partition_{env_prefix}('{current_date.strftime('%Y-%m-%d')}');")
            db.session.execute(partition_sql)
            logger.info(f"创建分区: {env_prefix}time_series_data_{current_date.strftime('%Y_%m')}")
        
        db.session.commit()
        logger.info("分区创建完成")
        return True
        
    except Exception as e:
        logger.error(f"创建分区失败: {e}")
        db.session.rollback()
        return False


def create_indexes(env_prefix):
    """创建优化索引"""
    try:
        logger.info(f"创建优化索引 ({env_prefix}环境)...")
        
        indexes = [
            # 主要查询索引
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_device_time ON {env_prefix}time_series_data USING BTREE (device_id, timestamp DESC);",
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_time_device ON {env_prefix}time_series_data USING BTREE (timestamp DESC, device_id);",
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_type_time ON {env_prefix}time_series_data USING BTREE (data_type, timestamp DESC);",
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_device_type_time ON {env_prefix}time_series_data USING BTREE (device_id, data_type, timestamp DESC);",
            
            # 数值范围查询索引
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_voltage_range ON {env_prefix}time_series_data USING BTREE (voltage) WHERE voltage IS NOT NULL;",
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_temperature_range ON {env_prefix}time_series_data USING BTREE (temperature) WHERE temperature IS NOT NULL;",
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_total_power_range ON {env_prefix}time_series_data USING BTREE (total_power) WHERE total_power IS NOT NULL;",
            
            # 聚合查询索引
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_hourly ON {env_prefix}time_series_data USING BTREE (device_id, date_trunc('hour', timestamp));",
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}ts_daily ON {env_prefix}time_series_data USING BTREE (device_id, date_trunc('day', timestamp));",
            
            # 缓冲表索引
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}buffer_created ON {env_prefix}time_series_buffer (created_at);",
            f"CREATE INDEX IF NOT EXISTS idx_{env_prefix}buffer_device ON {env_prefix}time_series_buffer (device_id);",
        ]
        
        for index_sql in indexes:
            try:
                db.session.execute(text(index_sql))
                logger.info(f"创建索引: {index_sql[:50]}...")
            except Exception as e:
                logger.warning(f"创建索引失败: {e}")
        
        db.session.commit()
        logger.info("索引创建完成")
        return True
        
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        db.session.rollback()
        return False


def create_maintenance_functions(env_prefix):
    """创建维护函数"""
    try:
        logger.info(f"创建维护函数 ({env_prefix}环境)...")
        
        # 缓冲区刷新函数
        flush_function_sql = f"""
        CREATE OR REPLACE FUNCTION flush_buffer_to_main_{env_prefix}()
        RETURNS integer AS $$
        DECLARE
            moved_count integer;
        BEGIN
            WITH moved_data AS (
                DELETE FROM {env_prefix}time_series_buffer 
                WHERE created_at < NOW() - interval '30 seconds'
                RETURNING *
            )
            INSERT INTO {env_prefix}time_series_data 
            (device_id, timestamp, data_type, power_values, voltage, temperature,
             total_power, csq, ber, error_counts, relay_state, relay_bits,
             zero_cross_time, created_at)
            SELECT 
                device_id, timestamp, data_type, power_values, voltage, temperature,
                total_power, csq, ber, error_counts, relay_state, relay_bits,
                zero_cross_time, created_at
            FROM moved_data;
            
            GET DIAGNOSTICS moved_count = ROW_COUNT;
            RETURN moved_count;
        END;
        $$ LANGUAGE plpgsql;
        """
        
        db.session.execute(text(flush_function_sql))

        # 分区维护函数
        maintenance_function_sql = f"""
        CREATE OR REPLACE FUNCTION maintain_partitions_{env_prefix}()
        RETURNS void AS $$
        BEGIN
            -- 创建下个月的分区
            PERFORM create_monthly_partition_{env_prefix}(
                date_trunc('month', CURRENT_DATE + interval '1 month')::date
            );

            -- 清理12个月前的分区
            PERFORM cleanup_old_partitions_{env_prefix}(12);
        END;
        $$ LANGUAGE plpgsql;
        """

        db.session.execute(text(maintenance_function_sql))

        # 清理函数
        cleanup_function_sql = f"""
        CREATE OR REPLACE FUNCTION cleanup_old_partitions_{env_prefix}(retention_months integer)
        RETURNS void AS $$
        DECLARE
            cutoff_date date;
            partition_name text;
        BEGIN
            cutoff_date := date_trunc('month', CURRENT_DATE - interval '1 month' * retention_months)::date;

            FOR partition_name IN
                SELECT schemaname||'.'||tablename
                FROM pg_tables
                WHERE tablename LIKE '{env_prefix}time_series_data_%'
                AND tablename < '{env_prefix}time_series_data_' || to_char(cutoff_date, 'YYYY_MM')
            LOOP
                EXECUTE 'DROP TABLE IF EXISTS ' || partition_name;
                RAISE NOTICE 'Dropped old partition: %', partition_name;
            END LOOP;
        END;
        $$ LANGUAGE plpgsql;
        """

        db.session.execute(text(cleanup_function_sql))
        
        db.session.commit()
        logger.info("维护函数创建完成")
        return True
        
    except Exception as e:
        logger.error(f"创建维护函数失败: {e}")
        db.session.rollback()
        return False


def verify_deployment(env_prefix):
    """验证部署结果"""
    try:
        logger.info(f"验证部署结果 ({env_prefix}环境)...")
        
        # 检查表是否存在
        inspector = db.inspect(db.engine)
        existing_tables = inspector.get_table_names()
        
        required_tables = [
            f'{env_prefix}time_series_data',
            f'{env_prefix}time_series_buffer'
        ]
        
        for table_name in required_tables:
            if table_name in existing_tables:
                logger.info(f"✓ 表 {table_name} 存在")
            else:
                logger.error(f"✗ 表 {table_name} 不存在")
                return False
        
        # 检查分区
        partition_query = f"""
        SELECT COUNT(*) FROM pg_tables 
        WHERE tablename LIKE '{env_prefix}time_series_data_%'
        """
        
        partition_count = db.session.execute(text(partition_query)).scalar()
        logger.info(f"✓ 找到 {partition_count} 个分区")

        # 检查函数
        function_query = f"""
        SELECT COUNT(*) FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname LIKE '%{env_prefix}%'
        """

        function_count = db.session.execute(text(function_query)).scalar()
        logger.info(f"✓ 找到 {function_count} 个维护函数")
        
        logger.info("部署验证成功")
        return True
        
    except Exception as e:
        logger.error(f"部署验证失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("部署优化时序数据库")
    print("=" * 60)
    
    # 设置环境变量
    env = input("请选择环境 (development/production) [development]: ").strip()
    if not env:
        env = 'development'
    
    os.environ['FLASK_ENV'] = env
    env_prefix = 'dev_' if env == 'development' else 'prod_'
    
    print(f"当前环境: {env}")
    print(f"表前缀: {env_prefix}")
    
    # 确认操作
    confirm = input(f"\n警告：此操作将删除现有的时序数据表！\n确定要继续吗？(yes/no): ").strip().lower()
    if confirm != 'yes':
        print("操作已取消")
        return False
    
    app = create_app()
    
    with app.app_context():
        # 1. 删除旧表
        print("\n1. 删除旧的时序数据表...")
        if not drop_old_tables(env_prefix):
            print("✗ 删除旧表失败")
            return False
        print("✓ 旧表删除成功")
        
        # 2. 创建优化表
        print("\n2. 创建优化的时序数据表...")
        if not create_optimized_tables(env_prefix):
            print("✗ 创建优化表失败")
            return False
        print("✓ 优化表创建成功")
        
        # 3. 创建分区
        print("\n3. 创建分区表...")
        if not create_partitions(env_prefix):
            print("✗ 创建分区失败")
            return False
        print("✓ 分区创建成功")
        
        # 4. 创建索引
        print("\n4. 创建优化索引...")
        if not create_indexes(env_prefix):
            print("✗ 创建索引失败")
            return False
        print("✓ 索引创建成功")
        
        # 5. 创建维护函数
        print("\n5. 创建维护函数...")
        if not create_maintenance_functions(env_prefix):
            print("✗ 创建维护函数失败")
            return False
        print("✓ 维护函数创建成功")
        
        # 6. 验证部署
        print("\n6. 验证部署结果...")
        if not verify_deployment(env_prefix):
            print("✗ 部署验证失败")
            return False
        print("✓ 部署验证成功")
    
    print("\n" + "=" * 60)
    print("优化时序数据库部署完成！")
    print("=" * 60)
    
    print("\n性能优化特点:")
    print("- 按月分区，提高查询性能")
    print("- 使用数组存储功率数据，减少记录数量")
    print("- 优化索引策略，支持各种查询模式")
    print("- 自动分区维护和数据清理")
    print("- 批量写入缓冲，减少锁竞争")
    
    print("\n下一步操作:")
    print("1. 更新应用代码使用新的时序数据服务")
    print("2. 重启应用服务")
    print("3. 测试数据写入和查询功能")
    print("4. 监控性能指标")
    
    return True


if __name__ == '__main__':
    main()
