{% extends "base.html" %}

{% block title %}信号质量历史数据 - {{ device.device_id }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
        margin-bottom: 2rem;
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
        margin-bottom: 10px;
    }
    .legend-container {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
    }
    
    /* 日期选择器样式增强 */
    .datepicker {
        z-index: 1050;
    }
    .datepicker table tr td.has-data {
        background-color: #28a745;
        color: white;
        font-weight: bold;
        border-radius: 4px;
    }
    .datepicker table tr td.day.has-data:hover {
        background-color: #218838;
    }
    td.day.has-data {
        background-color: #218838;
    }
    .datepicker table tr td.active.has-data {
        background-color: #1e7e34 
    }
    #datePickerGroup .input-group-text {
        cursor: pointer;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-signal text-success me-2"></i>信号质量历史数据 - {{ device.device_id }}
                        <span class="badge bg-info ms-2">{{ device.device_remark or '无备注' }}</span>
                    </h5>
                    <div>
                        <a href="{{ url_for('device_parameters.device_parameters', id=device.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回参数页面
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选条件 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>筛选条件</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- 日期选择器 -->
                                <div class="col-md-6">
                                    <label for="datePicker" class="form-label">日期</label>
                                    <div class="input-group date" id="datePickerGroup">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="text" class="form-control" id="datePicker" readonly>
                                    </div>
                                </div>

                                <!-- 时间范围 -->
                                <div class="col-md-6">
                                    <label for="timeRangeSelect" class="form-label">时间范围</label>
                                    <select class="form-select" id="timeRangeSelect">
                                        <option value="all" selected>全天</option>
                                        <option value="morning">上午 (6:00-12:00)</option>
                                        <option value="afternoon">下午 (12:00-18:00)</option>
                                        <option value="evening">晚上 (18:00-24:00)</option>
                                        <option value="night">凌晨 (0:00-6:00)</option>
                                    </select>
                                </div>

                                <!-- 查询按钮 -->
                                <div class="col-12 text-end">
                                    <button class="btn btn-primary" onclick="loadCsqData()">
                                        <i class="fas fa-search me-1"></i> 查询数据
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                        <i class="fas fa-redo me-1"></i> 重置筛选
                                    </button>
                                    <button class="btn btn-success ms-2" id="exportDataBtn">
                                        <i class="fas fa-download me-1"></i> 导出数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载中提示 -->
                    <div id="loadingIndicator" class="text-center py-5 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载信号质量数据，请稍候...</p>
                    </div>

                    <!-- 图表容器 -->
                    <div id="chartContainer" class="d-none">
                        <!-- 数据摘要 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-calculator me-2"></i>平均信号强度</h6>
                                        <h3 class="mb-0" id="avgCsq">--</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-arrow-up me-2"></i>最高信号强度</h6>
                                        <h3 class="mb-0" id="maxCsq">--</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-arrow-down me-2"></i>最低信号强度</h6>
                                        <h3 class="mb-0" id="minCsq">--</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-chart-line me-2"></i>数据点数</h6>
                                        <h3 class="mb-0" id="dataPoints">--</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 信号质量说明 -->
                        <div class="alert alert-info mb-4">
                            <h6><i class="fas fa-info-circle me-2"></i>信号质量说明</h6>
                            <p class="mb-0">CSQ值范围为0-31，值越大表示信号越强。通常CSQ值大于15表示信号良好，小于10表示信号较弱。</p>
                            <hr>
                            <p class="mb-1"><strong>CSQ值对应的接收信号强度(dBm)：</strong></p>
                            <ul class="mb-1 ps-3">
                                <li>0: 小于等于-115dBm</li>
                                <li>1: -111dBm</li>
                                <li>2~30: -109dBm ~ -53dBm</li>
                                <li>31: 大于等于-51dBm</li>
                                <li>99: 未知或不可测</li>
                            </ul>
                            <p class="mb-0"><small>计算公式: CSQ = (接收信号强度dBm + 113) / 2</small></p>
                            <hr>
                            <p class="mb-1"><strong>BER值说明：</strong></p>
                            <ul class="mb-1 ps-3">
                                <li>0~7: 对应GSM 05.08 section 8.2.4所示的RXQUAL值</li>
                                <li>99: 未知或不可测</li>
                            </ul>
                            <p class="mb-0"><small>注：BER（Bit Error Rate，误码率）仅在通道建立后才能获取该值</small></p>
                        </div>

                        <!-- 图表控制 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleChartType('line')">
                                    <i class="fas fa-chart-line me-1"></i> 折线图
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleChartType('bar')">
                                    <i class="fas fa-chart-bar me-1"></i> 柱状图
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="resetZoomBtn">
                                    <i class="fas fa-search-minus me-1"></i> 重置缩放
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="form-check form-switch me-3">
                                    <input class="form-check-input" type="checkbox" id="smoothLines" checked>
                                    <label class="form-check-label" for="smoothLines">平滑曲线</label>
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-info-circle me-1"></i> 可滚轮缩放、拖动平移
                                </div>
                            </div>
                        </div>

                        <!-- 图例 -->
                        <div class="legend-container mb-3" id="chartLegend"></div>

                        <!-- 图表 -->
                        <div class="chart-container">
                            <canvas id="csqChart"></canvas>
                        </div>

                        <!-- 图表说明 -->
                        <div class="mt-3 text-muted small">
                            <i class="fas fa-info-circle me-1"></i> 提示：双击图表可以重置缩放。
                        </div>
                    </div>

                    <!-- 无数据提示 -->
                    <div id="noDataMessage" class="alert alert-info d-none">
                        <i class="fas fa-info-circle me-2"></i> 所选日期没有信号质量数据。请确保调试脚本已运行并收集了数据。
                    </div>

                    <!-- 错误提示 -->
                    <div id="errorMessage" class="alert alert-danger d-none">
                        获取信号质量数据失败，请重试。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<!-- Datepicker CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/css/bootstrap-datepicker3.min.css">

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<!-- 添加Chart.js必要的适配器和插件 -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom"></script>

<!-- Datepicker JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/locales/bootstrap-datepicker.zh-CN.min.js"></script>
<script>
    // 图表对象
    let csqChart;
    let currentChartType = 'line';

    // 图表颜色
    const chartColor = 'rgb(75, 192, 192)';
    const chartColors = [
        'rgb(255, 99, 132)',   // 红色
        'rgb(54, 162, 235)',   // 蓝色
        'rgb(255, 206, 86)',   // 黄色
        'rgb(75, 192, 192)',   // 青色
        'rgb(153, 102, 255)',  // 紫色
        'rgb(255, 159, 64)'    // 橙色
    ];

    // 注册Chart.js插件
    Chart.register(ChartZoom);

    // 初始化日期选择器
    function initDatepicker() {
        const $datePicker = $('#datePicker');
        let availableDates = [];
        
        // 获取有数据的日期
        fetch('/debug_script/api/device/{{ device.id }}/data_dates')
            .then(response => response.json())
            .then(data => {
                availableDates = data.dates;
                
                // 初始化日期选择器
                $datePicker.datepicker({
                    format: 'yyyy-mm-dd',
                    language: 'zh-CN',
                    autoclose: true,
                    todayHighlight: true,
                    endDate: new Date(),
                    beforeShowDay: function(date) {
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        const dateString = `${year}-${month}-${day}`;
                        return {
                            tooltip: availableDates.includes(dateString) ? '有数据' : '无数据',
                            classes: availableDates.includes(dateString) ? 'has-data' : ''
                        };
                    }
                });
                
                // 设置默认日期为今天并自动加载数据
                const today = new Date();
                const todayStr = today.getFullYear() + '-' + 
                                String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                                String(today.getDate()).padStart(2, '0');
                $datePicker.val(todayStr);
                loadCsqData();
                
                // 监听日期变化
                $datePicker.on('changeDate', function() {
                    loadCsqData();
                });
            });
    }

    // 定义animateOnScroll函数，修复base.html中的引用错误
    function animateOnScroll() {
        // 空实现，仅用于防止错误
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化日期选择器
        initDatepicker();
        
        // 初始化平滑曲线开关事件
        document.getElementById('smoothLines').addEventListener('change', function() {
            if (csqChart) {
                const tension = this.checked ? 0.4 : 0;
                csqChart.data.datasets.forEach(dataset => {
                    dataset.tension = tension;
                });
                csqChart.update();
            }
        });

        // 为日期选择器添加变更事件
        document.getElementById('datePicker').addEventListener('change', function() {
            loadCsqData();
        });

        // 加载当天数据
        loadCsqData();
    });

    // 重置筛选条件
    function resetFilters() {
        // 重置日期为今天
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        document.getElementById('datePicker').value = `${year}-${month}-${day}`;

        // 重置时间范围为全天
        document.getElementById('timeRangeSelect').value = 'all';

        // 重新加载数据
        loadCsqData();
    }

    // 切换图表类型
    function toggleChartType(type) {
        if (csqChart && type !== currentChartType) {
            currentChartType = type;
            csqChart.config.type = type;

            // 根据图表类型调整样式
            if (type === 'line') {
                csqChart.data.datasets.forEach(dataset => {
                    dataset.borderWidth = 2;
                    dataset.pointRadius = 3;
                    dataset.pointHoverRadius = 5;
                    dataset.tension = document.getElementById('smoothLines').checked ? 0.4 : 0;
                });
            } else if (type === 'bar') {
                csqChart.data.datasets.forEach(dataset => {
                    dataset.borderWidth = 1;
                    dataset.borderColor = dataset.backgroundColor.replace('20', '');
                    dataset.backgroundColor = dataset.backgroundColor.replace('20', '80');
                });
            }

            csqChart.update();
        }
    }

    // 加载信号质量数据
    function loadCsqData() {
        // 获取选择的日期
        const dateInput = document.getElementById('datePicker');
        const date = dateInput.value;

        if (!date) {
            // 如果没有选择日期，设置为今天
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            dateInput.value = `${year}-${month}-${day}`;
            // alert('未选择日期，已自动设置为今天');
            return;
        }

        // 显示加载中
        document.getElementById('loadingIndicator').classList.remove('d-none');
        document.getElementById('chartContainer').classList.add('d-none');
        document.getElementById('noDataMessage').classList.add('d-none');
        document.getElementById('errorMessage').classList.add('d-none');

        // 发送请求获取信号质量数据
        fetch(`/debug_script/csq_data/{{ device.id }}?date=${date}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载中
                document.getElementById('loadingIndicator').classList.add('d-none');

                if (data.error) {
                    // 显示错误信息
                    document.getElementById('errorMessage').textContent = '获取信号质量数据失败: ' + data.error;
                    document.getElementById('errorMessage').classList.remove('d-none');
                    return;
                }

                // 检查是否有数据
                const csqData = data.csq_data;
                const hasData = csqData && csqData.length > 0;

                if (!hasData) {
                    // 显示无数据提示
                    document.getElementById('noDataMessage').classList.remove('d-none');
                    return;
                }

                // 应用筛选条件
                const filteredData = filterCsqData(csqData);

                // 更新图表
                updateCsqChart(filteredData);

                // 显示图表容器
                document.getElementById('chartContainer').classList.remove('d-none');
            })
            .catch(error => {
                console.error('获取信号质量数据失败:', error);
                // 隐藏加载中，显示错误信息
                document.getElementById('loadingIndicator').classList.add('d-none');
                document.getElementById('errorMessage').textContent = '获取信号质量数据失败: ' + error.message;
                document.getElementById('errorMessage').classList.remove('d-none');
            });
    }

    // 筛选信号质量数据
    function filterCsqData(csqData) {
        // 获取筛选条件
        const timeRange = document.getElementById('timeRangeSelect').value;

        // 如果选择了全天，则不筛选
        if (timeRange === 'all') {
            return csqData;
        }

        // 筛选时间范围
        return csqData.filter(point => {
            const hour = new Date(point.time).getHours();

            switch (timeRange) {
                case 'morning':
                    return hour >= 6 && hour < 12;
                case 'afternoon':
                    return hour >= 12 && hour < 18;
                case 'evening':
                    return hour >= 18 && hour < 24;
                case 'night':
                    return hour >= 0 && hour < 6;
                default:
                    return true;
            }
        });
    }

    // 计算数据统计信息
    function calculateStats(dataPoints) {
        if (!dataPoints || dataPoints.length === 0) {
            return {
                totalPoints: 0,
                avgCsq: 0,
                maxCsq: 0,
                minCsq: 0
            };
        }

        let totalSum = 0;
        let maxCsq = -Infinity;
        let minCsq = Infinity;

        dataPoints.forEach(point => {
            const value = point.csq;
            totalSum += value;
            maxCsq = Math.max(maxCsq, value);
            minCsq = Math.min(minCsq, value);
        });

        const avgCsq = dataPoints.length > 0 ? totalSum / dataPoints.length : 0;

        return {
            totalPoints: dataPoints.length,
            avgCsq,
            maxCsq: maxCsq !== -Infinity ? maxCsq : 0,
            minCsq: minCsq !== Infinity ? minCsq : 0
        };
    }

    // 更新信号质量图表
    function updateCsqChart(csqData) {
        // 准备图表数据
        const dataPoints = csqData.map(point => ({
            x: new Date(point.time),
            y: point.csq,
            ber: point.ber // 保存BER值用于tooltip显示
        }));

        // 按时间排序数据点
        dataPoints.sort((a, b) => a.x - b.x);
        
        // 计算并更新统计信息
        const stats = calculateStats(csqData);
        document.getElementById('avgCsq').textContent = stats.avgCsq.toFixed(1);
        document.getElementById('maxCsq').textContent = stats.maxCsq.toFixed(0);
        document.getElementById('minCsq').textContent = stats.minCsq.toFixed(0);
        document.getElementById('dataPoints').textContent = stats.totalPoints;

        // 更新图例
        const legendContainer = document.getElementById('chartLegend');
        legendContainer.innerHTML = '';

        // 添加图例项
        const legendItem = document.createElement('div');
        legendItem.className = 'legend-item';
        legendItem.innerHTML = `
            <div style="width: 20px; height: 20px; background-color: ${chartColor}; margin-right: 8px; border-radius: 4px;"></div>
            <span>信号强度 (CSQ)</span>
        `;
        legendContainer.appendChild(legendItem);

        // 添加信号质量图例
        const qualityLegend = document.createElement('div');
        qualityLegend.className = 'd-flex flex-wrap ms-4';

        const qualities = [
            { level: '优', color: '#28a745', range: '≥20' },
            { level: '良', color: '#17a2b8', range: '15-19' },
            { level: '中', color: '#ffc107', range: '10-14' },
            { level: '差', color: '#dc3545', range: '<10' }
        ];

        qualities.forEach(q => {
            const qualityItem = document.createElement('div');
            qualityItem.className = 'legend-item';
            qualityItem.innerHTML = `
                <div style="width: 12px; height: 12px; background-color: ${q.color}; margin-right: 4px; border-radius: 50%;"></div>
                <span class="small">${q.level} (${q.range})</span>
            `;
            qualityLegend.appendChild(qualityItem);
        });

        legendContainer.appendChild(qualityLegend);

        // 销毁现有图表
        if (csqChart) {
            csqChart.destroy();
        }

        // 创建新图表
        const ctx = document.getElementById('csqChart').getContext('2d');
        csqChart = new Chart(ctx, {
            type: currentChartType,
            data: {
                datasets: [{
                    label: '信号强度 (CSQ)',
                    data: dataPoints,
                    borderColor: chartColor,
                    backgroundColor: chartColor + '20',
                    borderWidth: 2,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    tension: document.getElementById('smoothLines').checked ? 0.4 : 0,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                scales: {
                    x: {
                        type: 'time',
                        adapters: {
                            date: {
                                locale: 'zh-CN'
                            }
                        },
                        time: {
                            unit: 'hour',
                            displayFormats: {
                                hour: 'HH:mm'
                            },
                            tooltipFormat: 'YYYY-MM-DD HH:mm:ss'
                        },
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '信号强度 (CSQ)'
                        },
                        min: 0,
                        max: 31,
                        ticks: {
                            stepSize: 5
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false  // 隐藏默认图例，使用自定义图例
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const csq = context.parsed.y;
                                const ber = context.raw.ber;
                                let quality = '';
                                if (csq >= 20) quality = '优';
                                else if (csq >= 15) quality = '良';
                                else if (csq >= 10) quality = '中';
                                else quality = '差';

                                let dBm = '';
                                if (csq === 0) dBm = '≤-115dBm';
                                else if (csq === 1) dBm = '-111dBm';
                                else if (csq >= 2 && csq <= 30) dBm = `-${113 - (csq*2)}dBm`;
                                else if (csq === 31) dBm = '≥-51dBm';
                                else dBm = '未知';

                                let berText = ber === 99 ? '未知' : ber;

                                return [
                                    `信号强度(CSQ): ${csq} (${quality})`,
                                    `信号强度(dBm): ${dBm}`,
                                    `误码率(BER): ${berText}`
                                ];
                            }
                        }
                    },
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'xy'
                        },
                        zoom: {
                            wheel: {
                                enabled: true
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'xy'
                        }
                    }
                }
            }
        });

        // 添加双击重置缩放事件
        document.getElementById('csqChart').addEventListener('dblclick', function() {
            if (csqChart.resetZoom) {
                csqChart.resetZoom();
            }
        });

        // 添加重置缩放按钮事件
        document.getElementById('resetZoomBtn').addEventListener('click', function() {
            if (csqChart.resetZoom) {
                csqChart.resetZoom();
            }
        });
    }

    // 初始化数据导出功能
    document.addEventListener('DOMContentLoaded', function() {
        const exportBtn = document.getElementById('exportDataBtn');
        if (exportBtn) {
            exportBtn.setAttribute('data-device-id', '{{ device.id }}');

            if (window.DataExporter) {
                window.dataExporter = new DataExporter({{ device.id }});
                window.dataExporter.createExportButton = function() {};

                exportBtn.addEventListener('click', function() {
                    window.dataExporter.showExportModal();
                });
            }
        }
    });
</script>

<!-- 导出功能相关脚本 -->
<script src="{{ url_for('static', filename='js/data-interface-config.js') }}"></script>
<script src="{{ url_for('static', filename='js/optimized-data-loader.js') }}"></script>
<script src="{{ url_for('static', filename='js/data-export.js') }}"></script>
{% endblock %}
